# -*- coding: utf-8 -*-

from . import models
from . import wizard

def post_init_hook(cr, registry):
    """Post-installation hook to migrate qualification_state data to stages"""
    from odoo import api, SUPERUSER_ID

    env = api.Environment(cr, SUPERUSER_ID, {})

    # Check if qualification_state field exists (for migration from old version)
    cr.execute("SELECT column_name FROM information_schema.columns WHERE table_name='hr_applicant' AND column_name='qualification_state'")
    if cr.fetchone():
        # Migration mapping
        stage_mapping = {
            'initial': 'alwasead_job_position_database.stage_initial_qualification',
            'screening': 'alwasead_job_position_database.stage_sent_for_screening',
            'additional_info': 'alwasead_job_position_database.stage_additional_info_requested',
            'rejected': 'alwasead_job_position_database.stage_rejected_initial',
            'completed': 'alwasead_job_position_database.stage_initial_qualification_completed',
        }

        # Get all applicants with qualification_state
        cr.execute("SELECT id, qualification_state FROM hr_applicant WHERE qualification_state IS NOT NULL")
        applicants_data = cr.fetchall()

        for applicant_id, qualification_state in applicants_data:
            if qualification_state in stage_mapping:
                try:
                    stage = env.ref(stage_mapping[qualification_state], raise_if_not_found=False)
                    if stage:
                        cr.execute("UPDATE hr_applicant SET stage_id = %s WHERE id = %s", (stage.id, applicant_id))
                except Exception:
                    # If stage not found, set to initial qualification
                    initial_stage = env.ref('alwasead_job_position_database.stage_initial_qualification', raise_if_not_found=False)
                    if initial_stage:
                        cr.execute("UPDATE hr_applicant SET stage_id = %s WHERE id = %s", (initial_stage.id, applicant_id))

        # Commit the migration
        cr.commit()
from . import controllers
