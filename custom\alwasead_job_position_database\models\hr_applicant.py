# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrApplicant(models.Model):
    _inherit = 'hr.applicant'

    # ==========================================
    # ALWASEAD CANDIDATE INFORMATION FIELDS
    # ==========================================
    
    nationality_id = fields.Many2one(
        'res.country',
        string='Nationality',
        help="Candidate's nationality",
        tracking=True
    )
    
    country_of_living_id = fields.Many2one(
        'res.country',
        string='Country of Living',
        help="Country where the candidate currently lives",
        tracking=True
    )
    
    academic_title = fields.Selection([
        ('high_school', 'High School Diploma'),
        ('diploma', 'Diploma'),
        ('bachelor', 'Bachelor\'s Degree'),
        ('master', 'Master\'s Degree'),
        ('phd', 'PhD/Doctorate'),
        ('professional', 'Professional Certificate'),
        ('other', 'Other')
    ], string='Academic Title (Degree)',
       help="Highest academic qualification achieved",
       tracking=True)
    
    work_experience_years = fields.Float(
        string='Years of Relevant Work Experience',
        help="Total years of relevant work experience",
        tracking=True
    )
    
    recruiting_source = fields.Selection([
        ('website', 'Company Website'),
        ('linkedin', 'LinkedIn'),
        ('facebook', 'Facebook'),
        ('libyanjob', 'LibyanJob'),
        ('mage', 'Service Provider - Mage'),
        ('pavo', 'Service Provider - Pavo'),
        ('art', 'Service Provider - Art'),
        ('mnw', 'Service Provider - MNW'),
        ('referral', 'Employee Referral'),
        ('other', 'Other')
    ], string='Source of Application',
       help="How did the candidate learn about this position?",
       tracking=True)
    
    # ==========================================
    # DOCUMENT UPLOADS
    # ==========================================
    
    diploma_attachment_id = fields.Many2one(
        'ir.attachment',
        string='Diploma',
        help="Upload diploma/degree certificate"
    )
    
    portfolio_attachment_id = fields.Many2one(
        'ir.attachment',
        string='Portfolio',
        help="Upload portfolio (for selected positions)"
    )
    
    # ==========================================
    # DATA PROTECTION COMPLIANCE
    # ==========================================
    
    data_protection_consent = fields.Boolean(
        string='Data Protection Consent',
        help="Candidate has agreed to data protection disclaimer",
        default=False,
        tracking=True
    )
    
    data_protection_consent_date = fields.Datetime(
        string='Consent Date',
        help="Date when candidate agreed to data protection terms",
        readonly=True
    )

    # ==========================================
    # STAGE TRANSITION TRACKING FIELDS
    # ==========================================

    stage_action_date = fields.Datetime(
        string='Last Stage Action Date',
        help="Date when the last stage action was taken",
        readonly=True
    )

    stage_action_by = fields.Many2one(
        'res.users',
        string='Stage Action Taken By',
        help="User who took the last stage action",
        readonly=True
    )

    additional_info_requested = fields.Text(
        string='Additional Information Requested',
        help="Details of additional information requested from candidate"
    )

    rejection_reason_initial = fields.Text(
        string='Initial Qualification Rejection Reason',
        help="Reason for rejection at initial qualification stage"
    )

    # TEMPORARILY ADDING BACK qualification_state FOR TESTING
    qualification_state = fields.Selection([
        ('initial', 'Initial Qualification'),
        ('screening', 'Sent for Screening'),
        ('additional_info', 'Additional Info Requested'),
        ('rejected', 'Rejected at Initial Qualification'),
        ('completed', 'Initial Qualification Completed'),
    ], string='Qualification State', default='initial', tracking=True)

    # Technical Interviewer Assignment
    technical_interviewer_id = fields.Many2one(
        'res.users',
        string='Technical Interviewer',
        domain="[('share', '=', False)]",
        help="Technical interviewer assigned from the relevant department",
        tracking=True
    )

    # Head of Department Assignment
    head_of_department_id = fields.Many2one(
        'res.users',
        string='Head of Department',
        domain="[('share', '=', False)]",
        help="Head of department for the applied position",
        tracking=True
    )
    
    # ==========================================
    # COMPUTED FIELDS
    # ==========================================
    
    @api.depends('work_experience_years')
    def _compute_experience_level(self):
        """Compute experience level based on years of experience"""
        for applicant in self:
            if applicant.work_experience_years:
                if applicant.work_experience_years < 1:
                    applicant.experience_level = 'entry'
                elif applicant.work_experience_years < 3:
                    applicant.experience_level = 'junior'
                elif applicant.work_experience_years < 7:
                    applicant.experience_level = 'mid'
                elif applicant.work_experience_years < 12:
                    applicant.experience_level = 'senior'
                else:
                    applicant.experience_level = 'expert'
            else:
                applicant.experience_level = False
    
    experience_level = fields.Selection([
        ('entry', 'Entry Level (0-1 years)'),
        ('junior', 'Junior (1-3 years)'),
        ('mid', 'Mid Level (3-7 years)'),
        ('senior', 'Senior (7-12 years)'),
        ('expert', 'Expert (12+ years)')
    ], string='Experience Level',
       compute='_compute_experience_level',
       store=True,
       help="Automatically computed based on years of experience")
    
    # ==========================================
    # CONSTRAINTS AND VALIDATIONS
    # ==========================================
    
    @api.constrains('work_experience_years')
    def _check_work_experience_years(self):
        """Validate work experience years"""
        for applicant in self:
            if applicant.work_experience_years and applicant.work_experience_years < 0:
                raise ValidationError(_('Work experience years cannot be negative.'))
            if applicant.work_experience_years and applicant.work_experience_years > 50:
                raise ValidationError(_('Work experience years seems unrealistic. Please verify.'))
    
    # Temporarily disabled to allow website form processing
    # @api.constrains('data_protection_consent')
    # def _check_data_protection_consent(self):
    #     """Ensure data protection consent is given"""
    #     for applicant in self:
    #         if not applicant.data_protection_consent:
    #             raise ValidationError(_(
    #                 'Data protection consent is mandatory. '
    #                 'The candidate must agree to the data protection disclaimer before submitting the application.'
    #             ))
    
    # ==========================================
    # METHODS
    # ==========================================
    
    @api.model
    def create(self, vals):
        """Override create to set consent date when consent is given and ensure name field"""

        # Handle data protection consent from website form
        # Website forms send checkbox values as strings ('1', 'on', etc.)
        consent_value = vals.get('data_protection_consent')
        if consent_value:
            if isinstance(consent_value, str):
                # Convert string values to boolean
                vals['data_protection_consent'] = consent_value.lower() in ('1', 'true', 'on', 'yes')
            if vals['data_protection_consent']:
                vals['data_protection_consent_date'] = fields.Datetime.now()

        # Validate data protection consent for website submissions
        # Check if this is a website form submission (has partner_name but no name)
        is_website_submission = vals.get('partner_name') and not vals.get('name')
        if is_website_submission and not vals.get('data_protection_consent'):
            raise ValidationError(_(
                'Data protection consent is mandatory. '
                'Please check the data protection consent checkbox before submitting your application.'
            ))

        # Ensure name field is set (required field)
        if not vals.get('name') and vals.get('partner_name'):
            vals['name'] = vals['partner_name']
        elif not vals.get('name'):
            vals['name'] = 'New Applicant'

        # Set default stage to Initial Qualification if not specified
        if not vals.get('stage_id'):
            try:
                initial_stage = self.env.ref('alwasead_job_position_database.stage_initial_qualification', raise_if_not_found=False)
                if initial_stage:
                    vals['stage_id'] = initial_stage.id
            except Exception:
                # If custom stage not found, let Odoo use default stage
                pass

        return super(HrApplicant, self).create(vals)

    # ==========================================
    # STAGE HELPER METHODS
    # ==========================================

    def _is_in_initial_qualification_stage(self):
        """Check if applicant is in Initial Qualification stage"""
        self.ensure_one()
        try:
            initial_stage = self.env.ref('alwasead_job_position_database.stage_initial_qualification', raise_if_not_found=False)
            return self.stage_id == initial_stage if initial_stage else False
        except Exception:
            return False

    def _is_in_screening_stage(self):
        """Check if applicant is in Sent for Screening stage"""
        self.ensure_one()
        try:
            screening_stage = self.env.ref('alwasead_job_position_database.stage_sent_for_screening', raise_if_not_found=False)
            return self.stage_id == screening_stage if screening_stage else False
        except Exception:
            return False

    def _is_in_additional_info_stage(self):
        """Check if applicant is in Additional Info Requested stage"""
        self.ensure_one()
        try:
            additional_info_stage = self.env.ref('alwasead_job_position_database.stage_additional_info_requested', raise_if_not_found=False)
            return self.stage_id == additional_info_stage if additional_info_stage else False
        except Exception:
            return False

    def _is_rejected_at_initial(self):
        """Check if applicant was rejected at initial qualification"""
        self.ensure_one()
        rejected_stage = self.env.ref('alwasead_job_position_database.stage_rejected_initial', raise_if_not_found=False)
        return self.stage_id == rejected_stage

    def _is_initial_qualification_completed(self):
        """Check if initial qualification is completed"""
        self.ensure_one()
        completed_stage = self.env.ref('alwasead_job_position_database.stage_initial_qualification_completed', raise_if_not_found=False)
        return self.stage_id == completed_stage

    # ==========================================
    # STAGE-BASED WORKFLOW ACTIONS
    # ==========================================

    def action_send_for_screening(self):
        """Send application for screening to technical interviewer"""
        self.ensure_one()

        # Get the "Sent for Screening" stage
        try:
            screening_stage = self.env.ref('alwasead_job_position_database.stage_sent_for_screening', raise_if_not_found=False)
            if not screening_stage:
                raise ValidationError(_('Screening stage not found. Please contact administrator.'))
        except Exception:
            raise ValidationError(_('Error accessing screening stage. Please contact administrator.'))

        # Update stage and tracking fields
        self.write({
            'stage_id': screening_stage.id,
            'stage_action_date': fields.Datetime.now(),
            'stage_action_by': self.env.user.id,
        })

        # Auto-assign technical interviewer if not set
        if not self.technical_interviewer_id and self.department_id:
            # Try to find a technical interviewer from the department
            dept_users = self.env['res.users'].search([
                ('department_id', '=', self.department_id.id),
                ('share', '=', False)
            ], limit=1)
            if dept_users:
                self.technical_interviewer_id = dept_users[0].id

        # Send notification email to technical interviewer (stage template will be used automatically)
        # Log message in chatter
        self.message_post(
            body=_("Application sent for screening by %s") % self.env.user.name,
            message_type='notification'
        )

        return True

    def action_request_additional_info(self):
        """Request additional information from candidate"""
        self.ensure_one()

        # Open wizard to specify what additional info is needed
        return {
            'name': _('Request Additional Information'),
            'type': 'ir.actions.act_window',
            'res_model': 'hr.applicant.additional.info.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_applicant_id': self.id,
            }
        }

    def action_reject_application(self):
        """Reject application at initial qualification stage"""
        self.ensure_one()

        # Open wizard to specify rejection reason
        return {
            'name': _('Reject Application'),
            'type': 'ir.actions.act_window',
            'res_model': 'hr.applicant.rejection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_applicant_id': self.id,
            }
        }

    def _send_additional_info_request(self, additional_info_text):
        """Send email requesting additional information"""
        # Get the "Additional Info Requested" stage
        additional_info_stage = self.env.ref('alwasead_job_position_database.stage_additional_info_requested', raise_if_not_found=False)
        if not additional_info_stage:
            raise ValidationError(_('Additional Info Requested stage not found. Please contact administrator.'))

        self.write({
            'stage_id': additional_info_stage.id,
            'stage_action_date': fields.Datetime.now(),
            'stage_action_by': self.env.user.id,
            'additional_info_requested': additional_info_text,
        })

        # Email will be sent automatically via stage template
        # Log message in chatter
        self.message_post(
            body=_("Additional information requested by %s: %s") % (self.env.user.name, additional_info_text),
            message_type='notification'
        )

    def _reject_application_initial(self, rejection_reason):
        """Reject application with reason"""
        # Get the "Rejected at Initial Qualification" stage
        rejected_stage = self.env.ref('alwasead_job_position_database.stage_rejected_initial', raise_if_not_found=False)
        if not rejected_stage:
            raise ValidationError(_('Rejected at Initial Qualification stage not found. Please contact administrator.'))

        self.write({
            'stage_id': rejected_stage.id,
            'stage_action_date': fields.Datetime.now(),
            'stage_action_by': self.env.user.id,
            'rejection_reason_initial': rejection_reason,
        })

        # Email will be sent automatically via stage template
        # Log message in chatter
        self.message_post(
            body=_("Application rejected by %s: %s") % (self.env.user.name, rejection_reason),
            message_type='notification'
        )

    def action_complete_initial_qualification(self):
        """Complete initial qualification and move to next stage"""
        self.ensure_one()

        # Get the "Initial Qualification Completed" stage
        completed_stage = self.env.ref('alwasead_job_position_database.stage_initial_qualification_completed', raise_if_not_found=False)
        if not completed_stage:
            raise ValidationError(_('Initial Qualification Completed stage not found. Please contact administrator.'))

        # Update stage and tracking fields
        self.write({
            'stage_id': completed_stage.id,
            'stage_action_date': fields.Datetime.now(),
            'stage_action_by': self.env.user.id,
        })

        # Log message in chatter
        self.message_post(
            body=_("Initial qualification completed by %s") % self.env.user.name,
            message_type='notification'
        )

        return True

    def write(self, vals):
        """Override write to update consent date and track stage changes"""
        # Handle data protection consent
        if vals.get('data_protection_consent') and not self.data_protection_consent_date:
            vals['data_protection_consent_date'] = fields.Datetime.now()

        # Track stage changes
        if vals.get('stage_id'):
            vals.update({
                'stage_action_date': fields.Datetime.now(),
                'stage_action_by': self.env.user.id,
            })

        return super(HrApplicant, self).write(vals)
    
    def action_view_diploma(self):
        """Action to view diploma attachment"""
        self.ensure_one()
        if not self.diploma_attachment_id:
            return {'type': 'ir.actions.act_window_close'}
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{self.diploma_attachment_id.id}',
            'target': 'new',
        }
    
    def action_view_portfolio(self):
        """Action to view portfolio attachment"""
        self.ensure_one()
        if not self.portfolio_attachment_id:
            return {'type': 'ir.actions.act_window_close'}
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{self.portfolio_attachment_id.id}',
            'target': 'new',
        }
    
    def get_application_summary(self):
        """Get a summary of the application for reporting"""
        self.ensure_one()
        return {
            'name': self.partner_name,
            'email': self.email_from,
            'nationality': self.nationality_id.name if self.nationality_id else '',
            'country_of_living': self.country_of_living_id.name if self.country_of_living_id else '',
            'academic_title': dict(self._fields['academic_title'].selection).get(self.academic_title, ''),
            'work_experience_years': self.work_experience_years,
            'recruiting_source': dict(self._fields['recruiting_source'].selection).get(self.recruiting_source, ''),
            'experience_level': dict(self._fields['experience_level'].selection).get(self.experience_level, ''),
            'has_diploma': bool(self.diploma_attachment_id),
            'has_portfolio': bool(self.portfolio_attachment_id),
            'data_protection_consent': self.data_protection_consent,
            'consent_date': self.data_protection_consent_date,
        }
