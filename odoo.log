2025-07-27 07:22:23,452 127172 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:22:23,457 127172 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:22:23,466 127172 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:22:23,491 127172 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:22:23,634 127172 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:22:23,825 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:22:23,847 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:22:23,854 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:22:23,862 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:22:23,867 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:22:23,871 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:22:23,879 127172 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:22:23,879 127172 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.model.data', <function IrModelData._xmlid_lookup at 0x000002C3B3E4ADE0>, 'alwasead_job_position_database.email_template_staffing_simple')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 568, in _tag_record
    f_val = self.id_get(f_ref, raise_if_not_found=nodeattr2bool(rec, 'forcecreate', True))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 681, in id_get
    res = self.model_id_get(id_str, raise_if_not_found)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 687, in model_id_get
    return self.env['ir.model.data']._xmlid_to_res_model_res_id(id_str, raise_if_not_found=raise_if_not_found)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2059, in _xmlid_to_res_model_res_id
    return self._xmlid_lookup(xmlid)[1:3]
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-43>", line 2, in _xmlid_lookup
  File "C:\odoo16\server\odoo\tools\cache.py", line 90, in lookup
    value = d[key] = self.method(*args, **kwargs)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2052, in _xmlid_lookup
    raise ValueError('External ID not found in the system: %s' % xmlid)
ValueError: External ID not found in the system: alwasead_job_position_database.email_template_staffing_simple

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 711, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/data/recruitment_stages_data.xml:17, somewhere inside
<record id="stage_sent_for_screening" model="hr.recruitment.stage">
            <field name="name">Sent for Screening</field>
            <field name="sequence">10</field>
            <field name="requirements">Application sent to technical interviewer for detailed screening</field>
            <field name="template_id" ref="email_template_staffing_simple"/>
            <field name="fold" eval="False"/>
        </record>
2025-07-27 07:22:23,882 127172 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.model.data', <function IrModelData._xmlid_lookup at 0x000002C3B3E4ADE0>, 'alwasead_job_position_database.email_template_staffing_simple')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 568, in _tag_record
    f_val = self.id_get(f_ref, raise_if_not_found=nodeattr2bool(rec, 'forcecreate', True))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 681, in id_get
    res = self.model_id_get(id_str, raise_if_not_found)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 687, in model_id_get
    return self.env['ir.model.data']._xmlid_to_res_model_res_id(id_str, raise_if_not_found=raise_if_not_found)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2059, in _xmlid_to_res_model_res_id
    return self._xmlid_lookup(xmlid)[1:3]
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-43>", line 2, in _xmlid_lookup
  File "C:\odoo16\server\odoo\tools\cache.py", line 90, in lookup
    value = d[key] = self.method(*args, **kwargs)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2052, in _xmlid_lookup
    raise ValueError('External ID not found in the system: %s' % xmlid)
ValueError: External ID not found in the system: alwasead_job_position_database.email_template_staffing_simple

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 711, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/data/recruitment_stages_data.xml:17, somewhere inside
<record id="stage_sent_for_screening" model="hr.recruitment.stage">
            <field name="name">Sent for Screening</field>
            <field name="sequence">10</field>
            <field name="requirements">Application sent to technical interviewer for detailed screening</field>
            <field name="template_id" ref="email_template_staffing_simple"/>
            <field name="fold" eval="False"/>
        </record>
2025-07-27 07:22:23,885 127172 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:22:23] "GET /web HTTP/1.1" 500 - 274 0.195 0.247
