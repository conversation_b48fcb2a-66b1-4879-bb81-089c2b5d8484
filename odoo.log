2025-07-27 07:35:47,600 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:35:47,612 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:35:47,627 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:35:47,701 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:35:47,842 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:35:47,998 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:35:48,069 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:35:48,084 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:35:48,096 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:35:48,119 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:35:48,129 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:35:48,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:35:48,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:35:48,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:35:48,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:35:48,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:35:48,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:35:48,210 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:35:48,251 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:35:48,257 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:35:48,277 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:35:48,287 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:35:48,302 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:35:48,318 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:35:48,363 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:35:48,383 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:35:48,383 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:35:48,384 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:35:48,384 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:35:48] "GET /web HTTP/1.1" 500 - 569 0.319 0.479
2025-07-27 07:35:53,607 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:35:53,607 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:35:53,622 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:35:53,655 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:35:53,757 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:35:53,868 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:35:53,899 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:35:53,906 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:35:53,906 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:35:53,916 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:35:53,916 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:35:53,916 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:35:53,916 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:35:53,916 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:35:53,926 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:35:53,928 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:35:53,931 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:35:53,931 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:35:53,931 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:35:53,947 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:35:53,947 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:35:53,947 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:35:53,979 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:35:53,995 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:35:54,011 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:35:54,091 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:35:54,148 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:35:54,160 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:35:54,175 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:35:54,229 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:35:54,298 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:35:54,298 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:35:54,304 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:35:54,307 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:35:54] "GET /websocket HTTP/1.1" 500 - 569 0.266 0.441
2025-07-27 07:36:09,180 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:36:09,185 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:36:09,203 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:36:09,260 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:36:09,616 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:36:09,939 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:36:10,023 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:36:10,046 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:36:10,070 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:36:10,070 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:36:10,086 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:36:10,096 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:36:10,102 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:36:10,108 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:36:10,112 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:36:10,130 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:36:10,134 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:36:10,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:36:10,158 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:36:10,190 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:36:10,190 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:36:10,190 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:36:10,313 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:36:10,360 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:36:10,376 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:36:10,408 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:36:10,468 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:36:10,505 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:36:10,539 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:36:10,600 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:36:10,650 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:36:10,654 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:36:10,655 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:36:10,657 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:36:10] "GET /websocket HTTP/1.1" 500 - 569 0.556 0.936
2025-07-27 07:36:33,661 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:36:33,674 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:36:33,693 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:36:33,768 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:36:33,955 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:36:34,328 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:36:34,417 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:36:34,433 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:36:34,449 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:36:34,465 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:36:34,479 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:36:34,482 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:36:34,487 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:36:34,492 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:36:34,496 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:36:34,513 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:36:34,513 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:36:34,528 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:36:34,544 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:36:34,585 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:36:34,587 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:36:34,595 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:36:34,731 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:36:34,796 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:36:34,828 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:36:34,859 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:36:34,926 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:36:34,963 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:36:34,996 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:36:35,088 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:36:35,125 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:36:35,127 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:36:35,127 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:36:35,127 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:36:35] "GET /websocket HTTP/1.1" 500 - 569 0.772 0.717
2025-07-27 07:37:10,113 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:37:10,130 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:37:10,252 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:37:10,288 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:37:10,426 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:37:10,692 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:37:10,786 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:37:10,796 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:37:10,802 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:37:10,821 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:37:10,829 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:37:10,829 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:37:10,835 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:37:10,835 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:37:10,835 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:37:10,849 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:37:10,849 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:37:10,864 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:37:10,864 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:37:10,883 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:37:10,896 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:37:10,896 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:37:10,999 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:37:11,068 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:37:11,095 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:37:11,127 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:37:11,175 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:37:11,206 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:37:11,238 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:37:11,302 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:37:11,351 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:37:11,351 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:37:11,353 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:37:11,353 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:37:11] "GET /websocket HTTP/1.1" 500 - 569 0.596 0.648
2025-07-27 07:38:04,608 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:38:04,608 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:38:04,640 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:38:04,695 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:38:04,838 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:38:05,109 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:38:05,194 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:38:05,209 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:38:05,227 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:38:05,242 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:38:05,249 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:38:05,249 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:38:05,258 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:38:05,264 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:38:05,274 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:38:05,284 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:38:05,292 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:38:05,292 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:38:05,292 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:38:05,352 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:38:05,363 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:38:05,368 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:38:05,463 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:38:05,543 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:38:05,574 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:38:05,610 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:38:05,670 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:38:05,698 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:38:05,731 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:38:05,803 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:38:05,838 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:38:05,838 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:38:05,845 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:38:05,846 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:38:05] "GET /websocket HTTP/1.1" 500 - 569 0.688 0.566
2025-07-27 07:39:06,903 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:39:06,917 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:39:06,941 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:39:07,306 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:39:07,811 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:39:08,209 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:39:08,331 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:39:08,337 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:39:08,367 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:39:08,381 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:39:08,398 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:39:08,402 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:39:08,402 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:39:08,414 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:39:08,414 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:39:08,437 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:39:08,446 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:39:08,446 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:39:08,476 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:39:08,518 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:39:08,524 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:39:08,531 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:39:08,696 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:39:08,792 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:39:08,820 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:39:08,853 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:39:08,928 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:39:08,968 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:39:09,015 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:39:09,115 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:39:09,186 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:39:09,186 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:39:09,198 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:39:09,201 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:39:09] "GET /websocket HTTP/1.1" 500 - 569 0.702 1.613
2025-07-27 07:40:09,545 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:40:09,559 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:40:09,573 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:40:09,604 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:40:09,725 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:40:10,026 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:40:10,120 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:40:10,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:40:10,154 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:40:10,166 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:40:10,174 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:40:10,177 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:40:10,184 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:40:10,190 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:40:10,196 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:40:10,205 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:40:10,217 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:40:10,222 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:40:10,230 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:40:10,259 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:40:10,263 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:40:10,268 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:40:10,398 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:40:10,473 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:40:10,505 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:40:10,540 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:40:10,597 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:40:10,624 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:40:10,657 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:40:10,725 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:40:10,776 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:40:10,776 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:40:10,782 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:40:10,783 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:40:10] "GET /websocket HTTP/1.1" 500 - 569 0.656 0.589
2025-07-27 07:41:12,093 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:41:12,110 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:41:12,121 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:41:12,146 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:41:15,008 150156 INFO ? odoo: Odoo version 16.0-20250210 
2025-07-27 07:41:15,008 150156 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:41:15,008 150156 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:41:15,008 150156 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:41:15,232 150156 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:41:15,506 150156 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:41:15,582 150156 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:41:15,598 150156 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:41:15,661 150156 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:41:15,671 150156 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 07:41:18,364 150156 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:41:18,364 150156 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:41:20,575 150156 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 07:41:20,672 150156 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 07:41:20,722 150156 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 07:41:23,096 150156 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:41:23,423 150156 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:41:23,653 150156 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:41:24,179 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:41:24,275 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:41:24,294 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:41:24,312 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:41:24,328 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:41:24,342 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:41:24,346 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:41:24,356 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:41:24,362 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:41:24,370 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:41:24,390 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:41:24,403 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:41:24,411 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:41:24,423 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:41:24,452 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:41:24,460 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:41:24,468 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:41:24,631 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:41:24,747 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:41:24,787 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:41:24,832 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:41:24,917 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:41:24,950 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:41:24,985 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:41:25,077 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:41:25,278 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:41:25,332 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:41:25,367 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:41:25,387 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:41:25,459 150156 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:41:25,656 150156 WARNING ardano_hr2 odoo.modules.loading: The models ['hr.applicant.additional.info.wizard', 'hr.applicant.rejection.wizard'] have no access rules in module alwasead_job_position_database, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
alwasead_job_position_database.access_hr_applicant_additional_info_wizard,access_hr_applicant_additional_info_wizard,alwasead_job_position_database.model_hr_applicant_additional_info_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_rejection_wizard,access_hr_applicant_rejection_wizard,alwasead_job_position_database.model_hr_applicant_rejection_wizard,base.group_user,1,0,0,0 
2025-07-27 07:41:25,672 150156 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 2.58s, 896 queries (+896 other) 
2025-07-27 07:41:25,673 150156 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 5.00s, 896 queries (+896 extra) 
2025-07-27 07:41:26,487 150156 INFO ardano_hr2 odoo.addons.base.models.ir_model: Deleting <EMAIL> (alwasead_job_position_database.field_hr_applicant__head_of_department_id) 
2025-07-27 07:41:26,592 150156 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [108550] 
2025-07-27 07:41:26,592 150156 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [7272] 
2025-07-27 07:41:26,594 150156 INFO ardano_hr2 odoo.addons.base.models.ir_model: Deleting <EMAIL> (alwasead_job_position_database.field_hr_applicant__technical_interviewer_id) 
2025-07-27 07:41:26,665 150156 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [108549] 
2025-07-27 07:41:26,665 150156 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [7271] 
2025-07-27 07:41:27,796 150156 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 07:41:27,820 150156 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 12.314s 
2025-07-27 07:41:27,931 150156 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 07:41:32,453 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:32] "GET /web HTTP/1.1" 200 - 178 0.252 16.683
2025-07-27 07:41:33,540 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:33] "GET /websocket HTTP/1.1" 101 - 2 0.005 0.067
2025-07-27 07:41:33,602 150156 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-27 07:41:33,643 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:33] "POST /mail/init_messaging HTTP/1.1" 200 - 57 0.130 0.100
2025-07-27 07:41:33,746 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:33] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.029 0.148
2025-07-27 07:41:33,775 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:33] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.057 0.046
2025-07-27 07:41:33,983 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:33] "POST /web/action/load HTTP/1.1" 200 - 14 0.019 0.633
2025-07-27 07:41:34,261 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:34] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 38 0.103 0.617
2025-07-27 07:41:34,291 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:34] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 66 0.115 0.174
2025-07-27 07:41:34,376 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:34] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 8 0.013 0.013
2025-07-27 07:41:34,448 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:34] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 7 0.008 0.013
2025-07-27 07:41:34,451 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:34] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.013 0.009
2025-07-27 07:41:34,478 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:34] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.005 0.008
2025-07-27 07:41:34,526 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:34] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 18 0.033 0.027
2025-07-27 07:41:34,641 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:34] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.008 0.011
2025-07-27 07:41:35,822 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:35] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 17 0.047 0.034
2025-07-27 07:41:36,138 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:36] "POST /mail/thread/data HTTP/1.1" 200 - 32 0.079 0.070
2025-07-27 07:41:36,141 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:36] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.089 0.065
2025-07-27 07:41:36,156 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:36] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.010 0.060
2025-07-27 07:41:36,177 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:36] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.029 0.056
2025-07-27 07:41:36,203 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:36] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.006 0.008
2025-07-27 07:41:36,245 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:36] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 16 0.030 0.029
2025-07-27 07:41:36,531 150156 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:41:36] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 32 0.036 0.435
2025-07-27 07:42:12,566 104200 INFO ? odoo: Odoo version 16.0-20250210 
2025-07-27 07:42:12,566 104200 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:42:12,567 104200 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:42:12,567 104200 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:42:12,721 104200 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:42:12,871 104200 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:42:12,904 104200 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:42:12,908 104200 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:42:12,972 104200 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:42:12,978 104200 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 07:42:15,377 104200 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:42:15,377 104200 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:42:16,507 104200 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 07:42:16,565 104200 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 07:42:16,602 104200 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 07:42:18,871 104200 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:42:19,205 104200 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:42:19,441 104200 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:42:19,862 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:42:19,953 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:42:19,971 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:42:19,987 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:42:20,003 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:42:20,016 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:42:20,020 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:42:20,032 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:42:20,040 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:42:20,049 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:42:20,070 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:42:20,083 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:42:20,093 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:42:20,103 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:42:20,125 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:42:20,133 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:42:20,141 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:42:20,297 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:42:20,396 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:42:20,452 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:42:20,503 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:42:20,595 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:42:20,633 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:42:20,672 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:42:20,748 104200 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:42:20,817 104200 WARNING ardano_hr2 odoo.modules.loading: Transient module states were reset 
2025-07-27 07:42:20,817 104200 ERROR ardano_hr2 odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                <group>
                    <group>
                        <field name="partner_id" invisible="1"/>
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>

Field "stage_action_by" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 93,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:42:20,822 104200 CRITICAL ardano_hr2 odoo.service.server: Failed to initialize database `ardano_hr2`. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\service\server.py", line 1323, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                <group>
                    <group>
                        <field name="partner_id" invisible="1"/>
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>

Field "stage_action_by" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 93,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:42:21,051 104200 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:42:21,067 104200 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:42:21,083 104200 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:42:21,245 104200 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:42:21,732 104200 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:42:22,039 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:42:22,322 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:42:22,338 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:42:22,359 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:42:22,374 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:42:22,385 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:42:22,389 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:42:22,396 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:42:22,402 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:42:22,409 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:42:22,420 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:42:22,433 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:42:22,444 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:42:22,453 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:42:22,482 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:42:22,487 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:42:22,491 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:42:22,613 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:42:22,716 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:42:22,766 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:42:22,807 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:42:22,872 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:42:22,904 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:42:22,937 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:42:23,021 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:42:23,090 104200 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:42:23,092 104200 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                <group>
                    <group>
                        <field name="partner_id" invisible="1"/>
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>

Field "stage_action_by" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 93,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:42:23,097 104200 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                <group>
                    <group>
                        <field name="partner_id" invisible="1"/>
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>

Field "stage_action_by" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 93,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:42:23,105 104200 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:42:23] "GET /web HTTP/1.1" 500 - 569 0.631 9.587
2025-07-27 07:42:23,116 104200 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:42:23,141 104200 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:42:23,169 104200 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:42:23,293 104200 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:42:23,759 104200 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:42:24,040 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:42:24,128 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:42:24,141 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:42:24,162 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:42:24,179 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:42:24,190 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:42:24,194 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:42:24,200 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:42:24,207 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:42:24,219 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:42:24,233 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:42:24,247 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:42:24,258 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:42:24,273 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:42:24,318 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:42:24,325 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:42:24,331 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:42:24,455 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:42:24,525 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:42:24,564 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:42:24,598 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:42:24,673 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:42:24,716 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:42:24,756 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:42:24,835 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:42:24,894 104200 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:42:24,894 104200 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                <group>
                    <group>
                        <field name="partner_id" invisible="1"/>
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>

Field "stage_action_by" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 93,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:42:24,897 104200 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                <group>
                    <group>
                        <field name="partner_id" invisible="1"/>
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>

Field "stage_action_by" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 93,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:42:24,900 104200 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:42:24] "GET /websocket HTTP/1.1" 500 - 569 0.606 11.044
2025-07-27 07:42:30,200 104200 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:42:30,236 104200 INFO ? odoo.modules.loading: 1 modules loaded in 0.04s, 0 queries (+0 extra) 
2025-07-27 07:42:30,259 104200 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:42:30,397 104200 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:42:30,835 104200 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:42:31,115 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:42:31,174 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:42:31,182 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:42:31,193 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:42:31,203 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:42:31,208 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:42:31,211 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:42:31,214 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:42:31,217 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:42:31,222 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:42:31,228 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:42:31,236 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:42:31,241 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:42:31,247 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:42:31,355 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:42:31,357 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:42:31,359 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:42:31,427 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:42:31,483 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:42:31,507 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:42:31,532 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:42:31,568 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:42:31,585 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:42:31,601 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:42:31,641 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:42:31,671 104200 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:42:31,671 104200 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                <group>
                    <group>
                        <field name="partner_id" invisible="1"/>
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>

Field "stage_action_by" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 93,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:42:31,672 104200 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                <group>
                    <group>
                        <field name="partner_id" invisible="1"/>
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>

Field "stage_action_by" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 93,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:42:31,673 104200 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:42:31] "GET /websocket HTTP/1.1" 500 - 569 0.525 0.961
2025-07-27 07:42:40,221 104200 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:42:40,231 104200 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:42:40,245 104200 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:42:40,297 104200 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:42:40,409 104200 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:42:40,723 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:42:40,791 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:42:40,801 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:42:40,811 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:42:40,823 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:42:40,830 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:42:40,832 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:42:40,840 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:42:40,846 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:42:40,850 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:42:40,857 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:42:40,868 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:42:40,876 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:42:40,883 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:42:40,905 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:42:40,908 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:42:40,912 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:42:40,991 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:42:41,038 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:42:41,060 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:42:41,092 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:42:41,144 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:42:41,179 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:42:41,213 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:42:41,281 104200 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:42:41,316 104200 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:42:41,316 104200 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                <group>
                    <group>
                        <field name="partner_id" invisible="1"/>
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>

Field "stage_action_by" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 93,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:42:41,318 104200 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                <group>
                    <group>
                        <field name="partner_id" invisible="1"/>
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>

Field "stage_action_by" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 93,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:42:41,319 104200 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:42:41] "GET /websocket HTTP/1.1" 500 - 569 0.607 0.502
