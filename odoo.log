2025-07-27 07:48:41,239 133956 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:48:41,247 133956 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:48:41,257 133956 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:48:41,315 133956 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:48:41,387 133956 CRITICAL ? odoo.modules.module: Couldn't load module alwasead_job_position_database 
2025-07-27 07:48:41,387 133956 CRITICAL ? odoo.modules.module: unmatched '}' (hr_applicant.py, line 501) 
2025-07-27 07:48:41,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:48:41,********** ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:41,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:41,********** INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:48:41] "GET /web HTTP/1.1" 500 - 13 0.011 0.152
