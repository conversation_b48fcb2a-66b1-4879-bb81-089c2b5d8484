2025-07-27 07:31:37,650 150236 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:31:37,654 150236 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:31:37,665 150236 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:31:37,709 150236 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:31:38,030 150236 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:31:38,238 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:31:38,275 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:31:38,280 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:31:38,288 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:31:38,291 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:31:38,295 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:31:38,297 150236 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:31:38,298 150236 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 808, in convert_xml_import
    doc = etree.parse(xmlfile)
          ^^^^^^^^^^^^^^^^^^^^
  File "src\\lxml\\etree.pyx", line 3570, in lxml.etree.parse
  File "src\\lxml\\parser.pxi", line 1973, in lxml.etree._parseDocument
  File "src\\lxml\\parser.pxi", line 1993, in lxml.etree._parseFilelikeDocument
  File "src\\lxml\\parser.pxi", line 1887, in lxml.etree._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 1224, in lxml.etree._BaseParser._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 633, in lxml.etree._ParserContext._handleParseResultDoc
  File "src\\lxml\\parser.pxi", line 743, in lxml.etree._handleParseResult
  File "src\\lxml\\parser.pxi", line 672, in lxml.etree._raiseParseError
  File "c:\odoo16\server\custom\alwasead_job_position_database\data\recruitment_stages_data.xml", line 54
lxml.etree.XMLSyntaxError: Double hyphen within comment: <!-- <record id="hr_recruitment.stage_job2" model="hr., line 54, column 11
2025-07-27 07:31:38,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 808, in convert_xml_import
    doc = etree.parse(xmlfile)
          ^^^^^^^^^^^^^^^^^^^^
  File "src\\lxml\\etree.pyx", line 3570, in lxml.etree.parse
  File "src\\lxml\\parser.pxi", line 1973, in lxml.etree._parseDocument
  File "src\\lxml\\parser.pxi", line 1993, in lxml.etree._parseFilelikeDocument
  File "src\\lxml\\parser.pxi", line 1887, in lxml.etree._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 1224, in lxml.etree._BaseParser._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 633, in lxml.etree._ParserContext._handleParseResultDoc
  File "src\\lxml\\parser.pxi", line 743, in lxml.etree._handleParseResult
  File "src\\lxml\\parser.pxi", line 672, in lxml.etree._raiseParseError
  File "c:\odoo16\server\custom\alwasead_job_position_database\data\recruitment_stages_data.xml", line 54
lxml.etree.XMLSyntaxError: Double hyphen within comment: <!-- <record id="hr_recruitment.stage_job2" model="hr., line 54, column 11
2025-07-27 07:31:38,********** INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:31:38] "GET /web HTTP/1.1" 500 - 255 0.223 0.437
