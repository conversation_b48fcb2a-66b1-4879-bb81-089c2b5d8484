2025-07-27 07:48:41,239 133956 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:48:41,247 133956 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:48:41,257 133956 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:48:41,315 133956 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:48:41,387 133956 CRITICAL ? odoo.modules.module: Couldn't load module alwasead_job_position_database 
2025-07-27 07:48:41,387 133956 CRITICAL ? odoo.modules.module: unmatched '}' (hr_applicant.py, line 501) 
2025-07-27 07:48:41,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:48:41,********** ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:41,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:41,********** INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:48:41] "GET /web HTTP/1.1" 500 - 13 0.011 0.152
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:48:53,********** CRITICAL ? odoo.modules.module: Couldn't load module alwasead_job_position_database 
2025-07-27 07:48:53,********** CRITICAL ? odoo.modules.module: unmatched '}' (hr_applicant.py, line 501) 
2025-07-27 07:48:53,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:48:53,********** ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:53,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:53,********** INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:48:53] "GET /websocket HTTP/1.1" 500 - 13 0.018 0.197
2025-07-27 07:49:16,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:49:16,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:49:16,********** INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:49:17,********** INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:49:17,********** WARNING ? odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:49:17,********** INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:49:17,********** INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:49:20,704 143168 INFO ? odoo: Odoo version 16.0-20250210 
2025-07-27 07:49:20,704 143168 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:49:20,704 143168 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:49:20,704 143168 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:49:20,831 143168 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:49:21,021 143168 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:49:21,084 143168 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:49:21,084 143168 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:49:21,116 143168 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:49:21,132 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 07:49:23,193 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:49:23,201 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:49:24,484 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 07:49:24,533 143168 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 07:49:24,553 143168 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 07:49:26,725 143168 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:49:27,059 143168 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:49:27,296 143168 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:49:27,805 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:49:27,910 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:49:27,917 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:49:27,949 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:49:27,967 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:49:27,979 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:49:27,996 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:49:28,001 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:49:28,012 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:49:28,028 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:49:28,050 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:49:28,054 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:49:28,077 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:49:28,096 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:49:28,100 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:49:28,297 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:49:28,393 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:49:28,428 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:49:28,463 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:49:28,551 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:49:28,613 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:49:28,664 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:49:28,762 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:49:28,933 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:49:28,984 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:49:29,051 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:49:29,068 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:49:29,123 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:49:29,444 143168 WARNING ardano_hr2 odoo.modules.loading: The models ['hr.applicant.additional.info.wizard', 'hr.applicant.rejection.wizard', 'hr.applicant.screening.rejection.wizard'] have no access rules in module alwasead_job_position_database, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
alwasead_job_position_database.access_hr_applicant_additional_info_wizard,access_hr_applicant_additional_info_wizard,alwasead_job_position_database.model_hr_applicant_additional_info_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_rejection_wizard,access_hr_applicant_rejection_wizard,alwasead_job_position_database.model_hr_applicant_rejection_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_screening_rejection_wizard,access_hr_applicant_screening_rejection_wizard,alwasead_job_position_database.model_hr_applicant_screening_rejection_wizard,base.group_user,1,0,0,0 
2025-07-27 07:49:29,466 143168 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 2.74s, 913 queries (+913 other) 
2025-07-27 07:49:29,466 143168 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 4.93s, 913 queries (+913 extra) 
2025-07-27 07:49:31,279 143168 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 07:49:31,287 143168 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 10.265s 
2025-07-27 07:49:31,443 143168 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 07:49:35,567 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:35] "GET /web HTTP/1.1" 200 - 178 0.234 14.120
2025-07-27 07:49:36,485 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "GET /websocket HTTP/1.1" 101 - 2 0.014 0.110
2025-07-27 07:49:36,553 143168 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-27 07:49:36,626 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "POST /mail/init_messaging HTTP/1.1" 200 - 57 0.196 0.091
2025-07-27 07:49:36,665 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.021 0.085
2025-07-27 07:49:36,707 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.024 0.024
2025-07-27 07:49:36,932 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "POST /web/action/load HTTP/1.1" 200 - 14 0.017 0.625
2025-07-27 07:49:37,201 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 38 0.086 0.577
2025-07-27 07:49:37,236 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 66 0.091 0.189
2025-07-27 07:49:37,294 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 8 0.010 0.014
2025-07-27 07:49:37,344 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 7 0.009 0.008
2025-07-27 07:49:37,346 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.009 0.008
2025-07-27 07:49:37,393 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.006 0.009
2025-07-27 07:49:37,427 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 18 0.025 0.024
2025-07-27 07:49:37,543 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.003 0.015
2025-07-27 07:49:38,642 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 17 0.031 0.025
2025-07-27 07:49:38,679 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.004 0.007
2025-07-27 07:49:38,812 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /mail/thread/data HTTP/1.1" 200 - 32 0.025 0.037
2025-07-27 07:49:38,813 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.035 0.031
2025-07-27 07:49:38,906 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.043 0.017
2025-07-27 07:49:39,275 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 32 0.029 0.467
2025-07-27 07:49:39,471 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/759/1920x160 HTTP/1.1" 304 - 8 0.009 0.047
2025-07-27 07:49:39,473 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/760/1920x160 HTTP/1.1" 304 - 8 0.015 0.047
2025-07-27 07:49:39,491 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.008 0.058
2025-07-27 07:49:39,503 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.018 0.058
2025-07-27 07:50:05,626 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:05] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.048 0.015
2025-07-27 07:50:05,647 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:05] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.056 0.025
2025-07-27 07:50:05,694 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:05] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.038 0.049
2025-07-27 07:50:05,871 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:05,871 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:10,276 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /web/dataset/call_button HTTP/1.1" 200 - 53 0.354 4.338
2025-07-27 07:50:10,320 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 07:50:10,336 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 07:50:10,382 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 17 0.041 0.020
2025-07-27 07:50:10,471 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.035 0.021
2025-07-27 07:50:10,477 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.027 0.026
2025-07-27 07:50:10,502 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.072 0.018
2025-07-27 07:50:16,872 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:16] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.016
2025-07-27 07:50:16,911 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:16] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.043 0.024
2025-07-27 07:50:16,923 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:16] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.032 0.031
2025-07-27 07:50:17,021 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:17] "POST /web/dataset/call_button HTTP/1.1" 200 - 35 0.076 0.098
2025-07-27 07:50:17,025 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 07:50:17,030 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 07:50:17,069 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:17] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.015 0.012
2025-07-27 07:50:17,085 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:17] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.025 0.021
2025-07-27 07:50:20,550 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.011
2025-07-27 07:50:20,559 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.007 0.017
2025-07-27 07:50:20,584 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.034 0.020
2025-07-27 07:50:20,597 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.023 0.026
2025-07-27 07:50:20,663 143168 INFO ardano_hr2 odoo.addons.base.models.ir_model: Access Denied by ACLs for operation: read, uid: 2, model: hr.applicant.screening.rejection.wizard 
2025-07-27 07:50:20,778 143168 WARNING ardano_hr2 odoo.http: You are not allowed to access 'Screening Rejection Wizard' (hr.applicant.screening.rejection.wizard) records.

No group currently allows this operation.

Contact your administrator to request access if necessary. 
2025-07-27 07:50:20,779 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /web/dataset/call_kw/hr.applicant.screening.rejection.wizard/get_views HTTP/1.1" 200 - 6 0.009 0.123
2025-07-27 07:50:20,825 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.014
2025-07-27 07:50:20,850 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.020 0.025
2025-07-27 07:50:27,935 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:27] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.009 0.010
2025-07-27 07:50:27,936 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:27] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.011 0.013
2025-07-27 07:50:27,978 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:27] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.007 0.008
2025-07-27 07:50:28,002 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:28] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 16 0.013 0.028
2025-07-27 07:50:29,483 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 14 0.027 0.021
2025-07-27 07:50:29,524 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.005 0.010
2025-07-27 07:50:29,649 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.025 0.034
2025-07-27 07:50:29,667 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.052 0.033
2025-07-27 07:50:31,996 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:31] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.018 0.019
2025-07-27 07:50:32,032 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:32] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.040 0.033
2025-07-27 07:50:32,079 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:32] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.043 0.057
2025-07-27 07:50:32,117 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:32,117 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:36,420 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /web/dataset/call_button HTTP/1.1" 200 - 35 0.109 4.345
2025-07-27 07:50:36,449 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 07:50:36,478 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 07:50:36,532 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 14 0.035 0.024
2025-07-27 07:50:36,591 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.023 0.015
2025-07-27 07:50:36,607 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.028 0.023
2025-07-27 07:50:36,613 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.037 0.025
2025-07-27 07:50:38,476 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.008 0.013
2025-07-27 07:50:38,480 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.014 0.020
2025-07-27 07:50:38,509 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.031 0.027
2025-07-27 07:50:38,529 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.027 0.036
2025-07-27 07:50:38,576 143168 INFO ardano_hr2 odoo.addons.base.models.ir_model: Access Denied by ACLs for operation: read, uid: 2, model: hr.applicant.screening.rejection.wizard 
2025-07-27 07:50:38,579 143168 WARNING ardano_hr2 odoo.http: You are not allowed to access 'Screening Rejection Wizard' (hr.applicant.screening.rejection.wizard) records.

No group currently allows this operation.

Contact your administrator to request access if necessary. 
2025-07-27 07:50:38,580 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /web/dataset/call_kw/hr.applicant.screening.rejection.wizard/get_views HTTP/1.1" 200 - 5 0.013 0.009
2025-07-27 07:50:38,601 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.004 0.007
2025-07-27 07:50:38,618 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.014 0.012
2025-07-27 07:51:29,000 149816 INFO ? odoo: Odoo version 16.0-20250210 
2025-07-27 07:51:29,000 149816 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:51:29,000 149816 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:51:29,000 149816 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:51:29,228 149816 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:51:29,381 149816 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:51:29,445 149816 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:51:29,456 149816 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:51:29,581 149816 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:51:29,586 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 07:51:32,050 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:51:32,050 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
