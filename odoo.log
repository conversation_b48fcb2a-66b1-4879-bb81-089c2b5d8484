2025-07-27 07:35:47,600 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:35:47,612 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:35:47,627 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:35:47,701 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:35:47,842 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:35:47,998 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:35:48,069 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:35:48,084 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:35:48,096 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:35:48,119 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:35:48,129 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:35:48,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:35:48,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:35:48,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:35:48,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:35:48,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:35:48,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:35:48,210 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:35:48,251 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:35:48,257 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:35:48,277 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:35:48,287 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:35:48,302 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:35:48,318 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:35:48,363 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:35:48,383 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:35:48,383 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:35:48,384 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:35:48,384 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:35:48] "GET /web HTTP/1.1" 500 - 569 0.319 0.479
2025-07-27 07:35:53,607 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:35:53,607 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:35:53,622 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:35:53,655 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:35:53,757 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:35:53,868 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:35:53,899 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:35:53,906 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:35:53,906 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:35:53,916 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:35:53,916 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:35:53,916 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:35:53,916 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:35:53,916 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:35:53,926 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:35:53,928 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:35:53,931 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:35:53,931 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:35:53,931 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:35:53,947 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:35:53,947 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:35:53,947 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:35:53,979 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:35:53,995 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:35:54,011 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:35:54,091 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:35:54,148 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:35:54,160 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:35:54,175 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:35:54,229 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:35:54,298 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:35:54,298 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:35:54,304 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:35:54,307 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:35:54] "GET /websocket HTTP/1.1" 500 - 569 0.266 0.441
2025-07-27 07:36:09,180 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:36:09,185 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:36:09,203 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:36:09,260 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:36:09,616 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:36:09,939 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:36:10,023 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:36:10,046 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:36:10,070 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:36:10,070 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:36:10,086 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:36:10,096 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:36:10,102 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:36:10,108 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:36:10,112 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:36:10,130 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:36:10,134 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:36:10,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:36:10,158 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:36:10,190 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:36:10,190 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:36:10,190 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:36:10,313 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:36:10,360 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:36:10,376 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:36:10,408 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:36:10,468 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:36:10,505 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:36:10,539 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:36:10,600 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:36:10,650 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:36:10,654 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:36:10,655 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:36:10,657 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:36:10] "GET /websocket HTTP/1.1" 500 - 569 0.556 0.936
2025-07-27 07:36:33,661 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:36:33,674 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:36:33,693 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:36:33,768 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:36:33,955 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:36:34,328 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:36:34,417 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:36:34,433 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:36:34,449 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:36:34,465 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:36:34,479 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:36:34,482 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:36:34,487 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:36:34,492 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:36:34,496 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:36:34,513 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:36:34,513 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:36:34,528 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:36:34,544 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:36:34,585 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:36:34,587 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:36:34,595 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:36:34,731 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:36:34,796 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:36:34,828 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:36:34,859 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:36:34,926 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:36:34,963 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:36:34,996 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:36:35,088 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:36:35,125 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:36:35,127 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:36:35,127 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:36:35,127 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:36:35] "GET /websocket HTTP/1.1" 500 - 569 0.772 0.717
2025-07-27 07:37:10,113 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:37:10,130 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:37:10,252 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:37:10,288 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:37:10,426 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:37:10,692 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:37:10,786 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:37:10,796 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:37:10,802 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:37:10,821 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:37:10,829 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:37:10,829 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:37:10,835 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:37:10,835 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:37:10,835 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:37:10,849 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:37:10,849 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:37:10,864 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:37:10,864 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:37:10,883 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:37:10,896 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:37:10,896 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:37:10,999 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:37:11,068 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:37:11,095 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:37:11,127 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:37:11,175 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:37:11,206 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:37:11,238 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:37:11,302 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:37:11,351 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:37:11,351 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:37:11,353 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:37:11,353 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:37:11] "GET /websocket HTTP/1.1" 500 - 569 0.596 0.648
2025-07-27 07:38:04,608 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:38:04,608 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:38:04,640 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:38:04,695 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:38:04,838 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:38:05,109 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:38:05,194 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:38:05,209 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:38:05,227 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:38:05,242 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:38:05,249 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:38:05,249 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:38:05,258 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:38:05,264 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:38:05,274 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:38:05,284 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:38:05,292 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:38:05,292 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:38:05,292 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:38:05,352 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:38:05,363 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:38:05,368 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:38:05,463 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:38:05,543 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:38:05,574 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:38:05,610 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:38:05,670 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:38:05,698 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:38:05,731 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:38:05,803 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:38:05,838 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:38:05,838 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:38:05,845 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:38:05,846 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:38:05] "GET /websocket HTTP/1.1" 500 - 569 0.688 0.566
2025-07-27 07:39:06,903 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:39:06,917 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:39:06,941 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:39:07,306 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:39:07,811 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:39:08,209 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:39:08,331 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:39:08,337 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:39:08,367 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:39:08,381 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:39:08,398 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:39:08,402 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:39:08,402 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:39:08,414 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:39:08,414 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:39:08,437 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:39:08,446 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:39:08,446 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:39:08,476 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:39:08,518 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:39:08,524 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:39:08,531 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:39:08,696 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:39:08,792 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:39:08,820 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:39:08,853 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:39:08,928 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:39:08,968 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:39:09,015 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:39:09,115 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:39:09,186 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:39:09,186 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:39:09,198 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:39:09,201 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:39:09] "GET /websocket HTTP/1.1" 500 - 569 0.702 1.613
2025-07-27 07:40:09,545 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:40:09,559 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:40:09,573 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:40:09,604 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:40:09,725 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:40:10,026 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:40:10,120 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:40:10,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:40:10,154 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:40:10,166 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:40:10,174 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:40:10,177 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:40:10,184 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:40:10,190 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:40:10,196 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:40:10,205 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:40:10,217 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:40:10,222 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:40:10,230 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:40:10,259 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:40:10,263 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:40:10,268 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:40:10,398 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:40:10,473 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:40:10,505 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:40:10,540 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:40:10,597 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:40:10,624 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:40:10,657 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:40:10,725 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:40:10,776 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:40:10,776 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:40:10,782 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:40:10,783 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:40:10] "GET /websocket HTTP/1.1" 500 - 569 0.656 0.589
2025-07-27 07:41:12,093 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:41:12,110 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:41:12,121 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:41:12,146 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:41:15,008 150156 INFO ? odoo: Odoo version 16.0-20250210 
2025-07-27 07:41:15,008 150156 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:41:15,008 150156 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:41:15,008 150156 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:41:15,232 150156 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:41:15,506 150156 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:41:15,582 150156 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:41:15,598 150156 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:41:15,661 150156 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:41:15,671 150156 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
