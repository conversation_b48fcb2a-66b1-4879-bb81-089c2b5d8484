2025-07-27 07:22:23,452 127172 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:22:23,457 127172 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:22:23,466 127172 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:22:23,491 127172 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:22:23,634 127172 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:22:23,825 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:22:23,847 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:22:23,854 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:22:23,862 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:22:23,867 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:22:23,871 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:22:23,879 127172 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:22:23,879 127172 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.model.data', <function IrModelData._xmlid_lookup at 0x000002C3B3E4ADE0>, 'alwasead_job_position_database.email_template_staffing_simple')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 568, in _tag_record
    f_val = self.id_get(f_ref, raise_if_not_found=nodeattr2bool(rec, 'forcecreate', True))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 681, in id_get
    res = self.model_id_get(id_str, raise_if_not_found)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 687, in model_id_get
    return self.env['ir.model.data']._xmlid_to_res_model_res_id(id_str, raise_if_not_found=raise_if_not_found)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2059, in _xmlid_to_res_model_res_id
    return self._xmlid_lookup(xmlid)[1:3]
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-43>", line 2, in _xmlid_lookup
  File "C:\odoo16\server\odoo\tools\cache.py", line 90, in lookup
    value = d[key] = self.method(*args, **kwargs)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2052, in _xmlid_lookup
    raise ValueError('External ID not found in the system: %s' % xmlid)
ValueError: External ID not found in the system: alwasead_job_position_database.email_template_staffing_simple

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 711, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/data/recruitment_stages_data.xml:17, somewhere inside
<record id="stage_sent_for_screening" model="hr.recruitment.stage">
            <field name="name">Sent for Screening</field>
            <field name="sequence">10</field>
            <field name="requirements">Application sent to technical interviewer for detailed screening</field>
            <field name="template_id" ref="email_template_staffing_simple"/>
            <field name="fold" eval="False"/>
        </record>
2025-07-27 07:22:23,882 127172 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.model.data', <function IrModelData._xmlid_lookup at 0x000002C3B3E4ADE0>, 'alwasead_job_position_database.email_template_staffing_simple')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 568, in _tag_record
    f_val = self.id_get(f_ref, raise_if_not_found=nodeattr2bool(rec, 'forcecreate', True))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 681, in id_get
    res = self.model_id_get(id_str, raise_if_not_found)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 687, in model_id_get
    return self.env['ir.model.data']._xmlid_to_res_model_res_id(id_str, raise_if_not_found=raise_if_not_found)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2059, in _xmlid_to_res_model_res_id
    return self._xmlid_lookup(xmlid)[1:3]
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-43>", line 2, in _xmlid_lookup
  File "C:\odoo16\server\odoo\tools\cache.py", line 90, in lookup
    value = d[key] = self.method(*args, **kwargs)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2052, in _xmlid_lookup
    raise ValueError('External ID not found in the system: %s' % xmlid)
ValueError: External ID not found in the system: alwasead_job_position_database.email_template_staffing_simple

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 711, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/data/recruitment_stages_data.xml:17, somewhere inside
<record id="stage_sent_for_screening" model="hr.recruitment.stage">
            <field name="name">Sent for Screening</field>
            <field name="sequence">10</field>
            <field name="requirements">Application sent to technical interviewer for detailed screening</field>
            <field name="template_id" ref="email_template_staffing_simple"/>
            <field name="fold" eval="False"/>
        </record>
2025-07-27 07:22:23,885 127172 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:22:23] "GET /web HTTP/1.1" 500 - 274 0.195 0.247
2025-07-27 07:22:36,611 127172 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:22:36,630 127172 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:22:36,646 127172 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:22:36,701 127172 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:22:36,812 127172 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:22:37,098 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:22:37,120 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:22:37,137 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:22:37,151 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:22:37,163 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:22:37,172 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:22:37,188 127172 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:22:37,189 127172 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.model.data', <function IrModelData._xmlid_lookup at 0x000002C3B3E4ADE0>, 'alwasead_job_position_database.email_template_staffing_simple')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 568, in _tag_record
    f_val = self.id_get(f_ref, raise_if_not_found=nodeattr2bool(rec, 'forcecreate', True))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 681, in id_get
    res = self.model_id_get(id_str, raise_if_not_found)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 687, in model_id_get
    return self.env['ir.model.data']._xmlid_to_res_model_res_id(id_str, raise_if_not_found=raise_if_not_found)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2059, in _xmlid_to_res_model_res_id
    return self._xmlid_lookup(xmlid)[1:3]
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-43>", line 2, in _xmlid_lookup
  File "C:\odoo16\server\odoo\tools\cache.py", line 90, in lookup
    value = d[key] = self.method(*args, **kwargs)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2052, in _xmlid_lookup
    raise ValueError('External ID not found in the system: %s' % xmlid)
ValueError: External ID not found in the system: alwasead_job_position_database.email_template_staffing_simple

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 711, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/data/recruitment_stages_data.xml:17, somewhere inside
<record id="stage_sent_for_screening" model="hr.recruitment.stage">
            <field name="name">Sent for Screening</field>
            <field name="sequence">10</field>
            <field name="requirements">Application sent to technical interviewer for detailed screening</field>
            <field name="template_id" ref="email_template_staffing_simple"/>
            <field name="fold" eval="False"/>
        </record>
2025-07-27 07:22:37,191 127172 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.model.data', <function IrModelData._xmlid_lookup at 0x000002C3B3E4ADE0>, 'alwasead_job_position_database.email_template_staffing_simple')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 568, in _tag_record
    f_val = self.id_get(f_ref, raise_if_not_found=nodeattr2bool(rec, 'forcecreate', True))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 681, in id_get
    res = self.model_id_get(id_str, raise_if_not_found)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\convert.py", line 687, in model_id_get
    return self.env['ir.model.data']._xmlid_to_res_model_res_id(id_str, raise_if_not_found=raise_if_not_found)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2059, in _xmlid_to_res_model_res_id
    return self._xmlid_lookup(xmlid)[1:3]
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-43>", line 2, in _xmlid_lookup
  File "C:\odoo16\server\odoo\tools\cache.py", line 90, in lookup
    value = d[key] = self.method(*args, **kwargs)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_model.py", line 2052, in _xmlid_lookup
    raise ValueError('External ID not found in the system: %s' % xmlid)
ValueError: External ID not found in the system: alwasead_job_position_database.email_template_staffing_simple

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 711, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/data/recruitment_stages_data.xml:17, somewhere inside
<record id="stage_sent_for_screening" model="hr.recruitment.stage">
            <field name="name">Sent for Screening</field>
            <field name="sequence">10</field>
            <field name="requirements">Application sent to technical interviewer for detailed screening</field>
            <field name="template_id" ref="email_template_staffing_simple"/>
            <field name="fold" eval="False"/>
        </record>
2025-07-27 07:22:37,193 127172 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:22:37] "GET /websocket HTTP/1.1" 500 - 274 0.220 0.378
2025-07-27 07:23:01,702 127172 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:23:01,707 127172 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:23:01,707 127172 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:23:01,752 127172 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:23:01,908 127172 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:23:02,129 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:23:02,156 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:23:02,160 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:23:02,160 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:23:02,168 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:23:02,168 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:23:02,193 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:23:02,197 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:23:02,199 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:23:02,202 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:23:02,208 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:23:02,208 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:23:02,208 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:23:02,223 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:23:02,223 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:23:02,223 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:23:02,307 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:23:02,352 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:23:02,384 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:23:02,402 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:23:02,457 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:23:02,484 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:23:02,511 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:23:02,548 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:23:02,603 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:23:02,612 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:23:02,644 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:23:02,644 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:23:02,684 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:23:02,880 127172 WARNING ? odoo.modules.loading: The models ['hr.applicant.additional.info.wizard', 'hr.applicant.rejection.wizard'] have no access rules in module alwasead_job_position_database, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
alwasead_job_position_database.access_hr_applicant_additional_info_wizard,access_hr_applicant_additional_info_wizard,alwasead_job_position_database.model_hr_applicant_additional_info_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_rejection_wizard,access_hr_applicant_rejection_wizard,alwasead_job_position_database.model_hr_applicant_rejection_wizard,base.group_user,1,0,0,0 
2025-07-27 07:23:02,884 127172 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:23:02,884 127172 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 305, in load_module_graph
    module.write({'state': 'installed', 'latest_version': ver})
  File "C:\odoo16\server\odoo\addons\website\models\ir_module_module.py", line 78, in write
    if request and request.db and request.context.get('apply_new_theme'):
                                  ^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1291, in context
    return self.env.context
           ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'context'
2025-07-27 07:23:02,918 127172 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:23:02] "GET /websocket HTTP/1.1" 404 - 928 0.623 0.605
2025-07-27 07:23:40,315 127172 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:23:40,315 127172 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:23:40,338 127172 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:23:40,382 127172 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:23:40,491 127172 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:23:40,736 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:23:40,894 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:23:40,903 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:23:40,920 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:23:40,927 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:23:40,934 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:23:40,971 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:23:40,980 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:23:40,989 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:23:40,999 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:23:41,012 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:23:41,025 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:23:41,032 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:23:41,056 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:23:41,061 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:23:41,063 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:23:41,167 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:23:41,240 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:23:41,277 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:23:41,319 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:23:41,388 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:23:41,423 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:23:41,451 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:23:41,533 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:23:41,595 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:23:41,616 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:23:41,630 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:23:41,640 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:23:41,660 127172 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:23:41,749 127172 WARNING ? odoo.modules.loading: The models ['hr.applicant.additional.info.wizard', 'hr.applicant.rejection.wizard'] have no access rules in module alwasead_job_position_database, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
alwasead_job_position_database.access_hr_applicant_additional_info_wizard,access_hr_applicant_additional_info_wizard,alwasead_job_position_database.model_hr_applicant_additional_info_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_rejection_wizard,access_hr_applicant_rejection_wizard,alwasead_job_position_database.model_hr_applicant_rejection_wizard,base.group_user,1,0,0,0 
2025-07-27 07:23:41,749 127172 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:23:41,749 127172 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 305, in load_module_graph
    module.write({'state': 'installed', 'latest_version': ver})
  File "C:\odoo16\server\odoo\addons\website\models\ir_module_module.py", line 78, in write
    if request and request.db and request.context.get('apply_new_theme'):
                                  ^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1291, in context
    return self.env.context
           ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'context'
2025-07-27 07:23:41,749 127172 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:23:41] "GET /websocket HTTP/1.1" 404 - 905 0.700 0.747
2025-07-27 07:24:01,990 142740 INFO ? odoo: Odoo version 16.0-20250210 
2025-07-27 07:24:01,990 142740 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:24:01,990 142740 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:24:01,990 142740 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:24:02,123 142740 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:24:02,253 142740 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:24:02,307 142740 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:24:02,327 142740 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:24:02,450 142740 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:24:02,456 142740 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 07:24:03,690 142740 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:24:03,690 142740 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:24:04,293 142740 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 07:24:04,328 142740 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 07:24:04,338 142740 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 07:24:04,913 142740 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:24:04,993 142740 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:24:05,040 142740 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:24:05,216 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:24:05,248 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:24:05,248 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:24:05,248 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:24:05,264 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:24:05,264 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:24:05,280 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:24:05,280 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:24:05,280 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:24:05,280 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:24:05,296 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:24:05,296 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:24:05,296 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:24:05,312 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:24:05,312 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:24:05,312 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:24:05,312 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:24:05,374 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:24:05,421 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:24:05,437 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:24:05,453 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:24:05,469 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:24:05,485 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:24:05,485 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:24:05,500 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:24:05,549 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:24:05,559 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:24:05,565 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:24:05,565 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:24:05,580 142740 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:24:05,645 142740 WARNING ardano_hr2 odoo.modules.loading: The models ['hr.applicant.additional.info.wizard', 'hr.applicant.rejection.wizard'] have no access rules in module alwasead_job_position_database, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
alwasead_job_position_database.access_hr_applicant_additional_info_wizard,access_hr_applicant_additional_info_wizard,alwasead_job_position_database.model_hr_applicant_additional_info_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_rejection_wizard,access_hr_applicant_rejection_wizard,alwasead_job_position_database.model_hr_applicant_rejection_wizard,base.group_user,1,0,0,0 
2025-07-27 07:24:05,645 142740 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 0.73s, 914 queries (+914 other) 
2025-07-27 07:24:05,645 142740 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 1.32s, 914 queries (+914 extra) 
2025-07-27 07:24:05,867 142740 INFO ardano_hr2 odoo.addons.base.models.ir_model: Deleting <EMAIL> (alwasead_job_position_database.selection__hr_applicant__qualification_state__completed) 
2025-07-27 07:24:05,883 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [128594] 
2025-07-27 07:24:05,883 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.fields.selection records with IDs: [1679] 
2025-07-27 07:24:05,883 142740 INFO ardano_hr2 odoo.addons.base.models.ir_model: Deleting <EMAIL> (alwasead_job_position_database.selection__hr_applicant__qualification_state__rejected) 
2025-07-27 07:24:05,899 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [128593] 
2025-07-27 07:24:05,899 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.fields.selection records with IDs: [1678] 
2025-07-27 07:24:05,899 142740 INFO ardano_hr2 odoo.addons.base.models.ir_model: Deleting <EMAIL> (alwasead_job_position_database.selection__hr_applicant__qualification_state__additional_info) 
2025-07-27 07:24:05,899 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [128592] 
2025-07-27 07:24:05,899 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.fields.selection records with IDs: [1677] 
2025-07-27 07:24:05,914 142740 INFO ardano_hr2 odoo.addons.base.models.ir_model: Deleting <EMAIL> (alwasead_job_position_database.selection__hr_applicant__qualification_state__screening) 
2025-07-27 07:24:05,914 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [128591] 
2025-07-27 07:24:05,914 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.fields.selection records with IDs: [1676] 
2025-07-27 07:24:05,914 142740 INFO ardano_hr2 odoo.addons.base.models.ir_model: Deleting <EMAIL> (alwasead_job_position_database.selection__hr_applicant__qualification_state__initial) 
2025-07-27 07:24:05,914 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [128590] 
2025-07-27 07:24:05,914 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.fields.selection records with IDs: [1675] 
2025-07-27 07:24:05,914 142740 INFO ardano_hr2 odoo.addons.base.models.ir_model: Deleting <EMAIL> (alwasead_job_position_database.field_hr_applicant__qualification_state) 
2025-07-27 07:24:05,947 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [128457] 
2025-07-27 07:24:05,947 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [7295] 
2025-07-27 07:24:05,947 142740 INFO ardano_hr2 odoo.addons.base.models.ir_model: Deleting <EMAIL> (alwasead_job_position_database.field_hr_applicant__qualification_action_by) 
2025-07-27 07:24:06,057 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [108546] 
2025-07-27 07:24:06,057 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [7268] 
2025-07-27 07:24:06,073 142740 INFO ardano_hr2 odoo.addons.base.models.ir_model: Deleting <EMAIL> (alwasead_job_position_database.field_hr_applicant__qualification_action_date) 
2025-07-27 07:24:06,089 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [108545] 
2025-07-27 07:24:06,089 142740 INFO ardano_hr2 odoo.models.unlink: User #1 deleted ir.model.fields records with IDs: [7267] 
2025-07-27 07:24:06,332 142740 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 07:24:06,332 142740 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 4.087s 
2025-07-27 07:24:06,408 142740 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
