2025-07-27 07:35:47,600 139976 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:35:47,612 139976 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:35:47,627 139976 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:35:47,701 139976 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:35:47,842 139976 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:35:47,998 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:35:48,069 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:35:48,084 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:35:48,096 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:35:48,103 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:35:48,119 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:35:48,129 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:35:48,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:35:48,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:35:48,135 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:35:48,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:35:48,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:35:48,150 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:35:48,210 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:35:48,251 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:35:48,257 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:35:48,277 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:35:48,287 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:35:48,302 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:35:48,318 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:35:48,363 139976 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:35:48,383 139976 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:35:48,383 139976 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:35:48,384 139976 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 829, in convert_xml_import
    obj.parse(doc.getroot())
  File "C:\odoo16\server\odoo\tools\convert.py", line 749, in parse
    self._tag_root(de)
  File "C:\odoo16\server\odoo\tools\convert.py", line 698, in _tag_root
    f(rec)
  File "C:\odoo16\server\odoo\tools\convert.py", line 709, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing file:/c:/odoo16/server/custom/alwasead_job_position_database/views/hr_applicant_views.xml:6
Error while validating view near:

                        <field name="partner_mobile" widget="phone"/>
                    <field name="nationality_id" options="{'no_create': True, 'no_edit': True}"/>
                    <field name="country_of_living_id" options="{'no_create': True, 'no_edit': True}"/>
                        <field name="linkedin_profile" widget="url"/>
                        <field name="type_id" placeholder="Degree"/>

Field "technical_interviewer_id" does not exist in model "hr.applicant"

View error context:
{'file': 'c:\\odoo16\\server\\custom\\alwasead_job_position_database\\views\\hr_applicant_views.xml',
 'line': 100,
 'name': 'hr.applicant.form.enhanced',
 'view': ir.ui.view(1600,),
 'view.model': 'hr.applicant',
 'view.parent': ir.ui.view(1092,),
 'xmlid': 'view_hr_applicant_form_enhanced'}

2025-07-27 07:35:48,384 139976 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:35:48] "GET /web HTTP/1.1" 500 - 569 0.319 0.479
