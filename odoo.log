2025-07-27 07:48:41,239 133956 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:48:41,247 133956 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:48:41,257 133956 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:48:41,315 133956 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:48:41,387 133956 CRITICAL ? odoo.modules.module: Couldn't load module alwasead_job_position_database 
2025-07-27 07:48:41,387 133956 CRITICAL ? odoo.modules.module: unmatched '}' (hr_applicant.py, line 501) 
2025-07-27 07:48:41,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:48:41,********** ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:41,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:41,********** INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:48:41] "GET /web HTTP/1.1" 500 - 13 0.011 0.152
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:48:53,********** INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:48:53,********** CRITICAL ? odoo.modules.module: Couldn't load module alwasead_job_position_database 
2025-07-27 07:48:53,********** CRITICAL ? odoo.modules.module: unmatched '}' (hr_applicant.py, line 501) 
2025-07-27 07:48:53,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:48:53,********** ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:53,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 189, in load_module_graph
    load_openerp_module(package.name)
  File "C:\odoo16\server\odoo\modules\module.py", line 471, in load_openerp_module
    __import__('odoo.addons.' + module_name)
  File "c:\odoo16\server\custom\alwasead_job_position_database\__init__.py", line 3, in <module>
    from . import models
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\__init__.py", line 12, in <module>
    from . import hr_applicant
  File "c:\odoo16\server\custom\alwasead_job_position_database\models\hr_applicant.py", line 501
    }
    ^
SyntaxError: unmatched '}'
2025-07-27 07:48:53,********** INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:48:53] "GET /websocket HTTP/1.1" 500 - 13 0.018 0.197
2025-07-27 07:49:16,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:49:16,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-07-27 07:49:16,********** INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:49:17,********** INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:49:17,********** WARNING ? odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:49:17,********** INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:49:17,********** INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:49:20,704 143168 INFO ? odoo: Odoo version 16.0-20250210 
2025-07-27 07:49:20,704 143168 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:49:20,704 143168 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:49:20,704 143168 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:49:20,831 143168 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:49:21,021 143168 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:49:21,084 143168 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:49:21,084 143168 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:49:21,116 143168 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:49:21,132 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 07:49:23,193 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:49:23,201 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:49:24,484 143168 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 07:49:24,533 143168 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 07:49:24,553 143168 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 07:49:26,725 143168 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:49:27,059 143168 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:49:27,296 143168 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:49:27,805 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:49:27,910 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:49:27,917 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:49:27,949 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:49:27,967 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:49:27,979 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:49:27,996 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:49:28,001 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:49:28,012 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:49:28,028 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:49:28,050 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:49:28,054 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:49:28,077 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:49:28,096 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:49:28,100 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:49:28,297 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:49:28,393 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:49:28,428 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:49:28,463 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:49:28,551 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:49:28,613 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:49:28,664 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:49:28,762 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:49:28,933 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:49:28,984 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:49:29,051 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:49:29,068 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:49:29,123 143168 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:49:29,444 143168 WARNING ardano_hr2 odoo.modules.loading: The models ['hr.applicant.additional.info.wizard', 'hr.applicant.rejection.wizard', 'hr.applicant.screening.rejection.wizard'] have no access rules in module alwasead_job_position_database, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
alwasead_job_position_database.access_hr_applicant_additional_info_wizard,access_hr_applicant_additional_info_wizard,alwasead_job_position_database.model_hr_applicant_additional_info_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_rejection_wizard,access_hr_applicant_rejection_wizard,alwasead_job_position_database.model_hr_applicant_rejection_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_screening_rejection_wizard,access_hr_applicant_screening_rejection_wizard,alwasead_job_position_database.model_hr_applicant_screening_rejection_wizard,base.group_user,1,0,0,0 
2025-07-27 07:49:29,466 143168 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 2.74s, 913 queries (+913 other) 
2025-07-27 07:49:29,466 143168 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 4.93s, 913 queries (+913 extra) 
2025-07-27 07:49:31,279 143168 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 07:49:31,287 143168 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 10.265s 
2025-07-27 07:49:31,443 143168 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 07:49:35,567 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:35] "GET /web HTTP/1.1" 200 - 178 0.234 14.120
2025-07-27 07:49:36,485 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "GET /websocket HTTP/1.1" 101 - 2 0.014 0.110
2025-07-27 07:49:36,553 143168 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-27 07:49:36,626 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "POST /mail/init_messaging HTTP/1.1" 200 - 57 0.196 0.091
2025-07-27 07:49:36,665 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.021 0.085
2025-07-27 07:49:36,707 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.024 0.024
2025-07-27 07:49:36,932 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:36] "POST /web/action/load HTTP/1.1" 200 - 14 0.017 0.625
2025-07-27 07:49:37,201 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 38 0.086 0.577
2025-07-27 07:49:37,236 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 66 0.091 0.189
2025-07-27 07:49:37,294 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 8 0.010 0.014
2025-07-27 07:49:37,344 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 7 0.009 0.008
2025-07-27 07:49:37,346 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.009 0.008
2025-07-27 07:49:37,393 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.006 0.009
2025-07-27 07:49:37,427 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 18 0.025 0.024
2025-07-27 07:49:37,543 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:37] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.003 0.015
2025-07-27 07:49:38,642 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 17 0.031 0.025
2025-07-27 07:49:38,679 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.004 0.007
2025-07-27 07:49:38,812 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /mail/thread/data HTTP/1.1" 200 - 32 0.025 0.037
2025-07-27 07:49:38,813 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /mail/thread/messages HTTP/1.1" 200 - 27 0.035 0.031
2025-07-27 07:49:38,906 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:38] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.043 0.017
2025-07-27 07:49:39,275 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 32 0.029 0.467
2025-07-27 07:49:39,471 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/759/1920x160 HTTP/1.1" 304 - 8 0.009 0.047
2025-07-27 07:49:39,473 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/760/1920x160 HTTP/1.1" 304 - 8 0.015 0.047
2025-07-27 07:49:39,491 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 304 - 8 0.008 0.058
2025-07-27 07:49:39,503 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:49:39] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.018 0.058
2025-07-27 07:50:05,626 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:05] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.048 0.015
2025-07-27 07:50:05,647 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:05] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.056 0.025
2025-07-27 07:50:05,694 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:05] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.038 0.049
2025-07-27 07:50:05,871 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:05,871 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:10,276 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /web/dataset/call_button HTTP/1.1" 200 - 53 0.354 4.338
2025-07-27 07:50:10,320 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 07:50:10,336 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 07:50:10,382 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 17 0.041 0.020
2025-07-27 07:50:10,471 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.035 0.021
2025-07-27 07:50:10,477 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.027 0.026
2025-07-27 07:50:10,502 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:10] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.072 0.018
2025-07-27 07:50:16,872 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:16] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.016
2025-07-27 07:50:16,911 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:16] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.043 0.024
2025-07-27 07:50:16,923 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:16] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.032 0.031
2025-07-27 07:50:17,021 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:17] "POST /web/dataset/call_button HTTP/1.1" 200 - 35 0.076 0.098
2025-07-27 07:50:17,025 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 07:50:17,030 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 07:50:17,069 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:17] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.015 0.012
2025-07-27 07:50:17,085 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:17] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.025 0.021
2025-07-27 07:50:20,550 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.011 0.011
2025-07-27 07:50:20,559 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.007 0.017
2025-07-27 07:50:20,584 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.034 0.020
2025-07-27 07:50:20,597 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.023 0.026
2025-07-27 07:50:20,663 143168 INFO ardano_hr2 odoo.addons.base.models.ir_model: Access Denied by ACLs for operation: read, uid: 2, model: hr.applicant.screening.rejection.wizard 
2025-07-27 07:50:20,778 143168 WARNING ardano_hr2 odoo.http: You are not allowed to access 'Screening Rejection Wizard' (hr.applicant.screening.rejection.wizard) records.

No group currently allows this operation.

Contact your administrator to request access if necessary. 
2025-07-27 07:50:20,779 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /web/dataset/call_kw/hr.applicant.screening.rejection.wizard/get_views HTTP/1.1" 200 - 6 0.009 0.123
2025-07-27 07:50:20,825 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.014
2025-07-27 07:50:20,850 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:20] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.020 0.025
2025-07-27 07:50:27,935 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:27] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.009 0.010
2025-07-27 07:50:27,936 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:27] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.011 0.013
2025-07-27 07:50:27,978 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:27] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.007 0.008
2025-07-27 07:50:28,002 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:28] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 16 0.013 0.028
2025-07-27 07:50:29,483 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 14 0.027 0.021
2025-07-27 07:50:29,524 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.005 0.010
2025-07-27 07:50:29,649 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.025 0.034
2025-07-27 07:50:29,667 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:29] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.052 0.033
2025-07-27 07:50:31,996 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:31] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.018 0.019
2025-07-27 07:50:32,032 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:32] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.040 0.033
2025-07-27 07:50:32,079 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:32] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.043 0.057
2025-07-27 07:50:32,117 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:32,117 143168 WARNING ardano_hr2 odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using <EMAIL> as fallback 
2025-07-27 07:50:36,420 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /web/dataset/call_button HTTP/1.1" 200 - 35 0.109 4.345
2025-07-27 07:50:36,449 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Starting job `Recruitment OCR: Parse CV`. 
2025-07-27 07:50:36,478 143168 INFO ardano_hr2 odoo.addons.base.models.ir_cron: Job `Recruitment OCR: Parse CV` done. 
2025-07-27 07:50:36,532 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 14 0.035 0.024
2025-07-27 07:50:36,591 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.023 0.015
2025-07-27 07:50:36,607 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.028 0.023
2025-07-27 07:50:36,613 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:36] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.037 0.025
2025-07-27 07:50:38,476 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /web/dataset/call_button HTTP/1.1" 200 - 4 0.008 0.013
2025-07-27 07:50:38,480 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.014 0.020
2025-07-27 07:50:38,509 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.031 0.027
2025-07-27 07:50:38,529 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.027 0.036
2025-07-27 07:50:38,576 143168 INFO ardano_hr2 odoo.addons.base.models.ir_model: Access Denied by ACLs for operation: read, uid: 2, model: hr.applicant.screening.rejection.wizard 
2025-07-27 07:50:38,579 143168 WARNING ardano_hr2 odoo.http: You are not allowed to access 'Screening Rejection Wizard' (hr.applicant.screening.rejection.wizard) records.

No group currently allows this operation.

Contact your administrator to request access if necessary. 
2025-07-27 07:50:38,580 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /web/dataset/call_kw/hr.applicant.screening.rejection.wizard/get_views HTTP/1.1" 200 - 5 0.013 0.009
2025-07-27 07:50:38,601 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.004 0.007
2025-07-27 07:50:38,618 143168 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:50:38] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.014 0.012
2025-07-27 07:51:29,000 149816 INFO ? odoo: Odoo version 16.0-20250210 
2025-07-27 07:51:29,000 149816 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:51:29,000 149816 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:51:29,000 149816 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:51:29,228 149816 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:51:29,381 149816 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:51:29,445 149816 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:51:29,456 149816 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:51:29,581 149816 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:51:29,586 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 07:51:32,050 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:51:32,050 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:51:33,704 149816 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 07:51:33,801 149816 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 07:51:33,842 149816 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 07:51:36,145 149816 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:51:36,460 149816 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:51:36,681 149816 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:51:37,236 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:51:37,352 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:51:37,379 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:51:37,414 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:51:37,427 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:51:37,448 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:51:37,459 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:51:37,463 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:51:37,463 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:51:37,500 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:51:37,518 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:51:37,527 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:51:37,567 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:51:37,583 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:51:37,593 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:51:37,781 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:51:37,888 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:51:37,925 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:51:37,953 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:51:38,034 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:51:38,077 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:51:38,109 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:51:38,205 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:51:38,379 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:51:38,428 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:51:38,492 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:51:38,524 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:51:38,594 149816 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:51:38,828 149816 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 2.68s, 905 queries (+905 other) 
2025-07-27 07:51:38,828 149816 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 5.03s, 905 queries (+905 extra) 
2025-07-27 07:51:40,615 149816 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 07:51:40,631 149816 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 11.254s 
2025-07-27 07:51:41,361 149816 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 07:51:41,363 149816 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 07:51:45,428 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:45] "GET /websocket HTTP/1.1" 101 - 8 0.073 15.707
2025-07-27 07:51:45,454 149816 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-27 07:51:48,646 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:48] "GET /web HTTP/1.1" 200 - 176 0.164 19.068
2025-07-27 07:51:49,412 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:49] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.014 0.019
2025-07-27 07:51:49,518 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:49] "POST /mail/init_messaging HTTP/1.1" 200 - 55 0.157 0.034
2025-07-27 07:51:49,566 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:49] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.013 0.019
2025-07-27 07:51:49,757 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:49] "POST /web/action/load HTTP/1.1" 200 - 14 0.000 0.457
2025-07-27 07:51:50,027 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 66 0.090 0.164
2025-07-27 07:51:50,075 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 38 0.105 0.608
2025-07-27 07:51:50,075 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 8 0.000 0.015
2025-07-27 07:51:50,142 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 15 0.033 0.013
2025-07-27 07:51:50,187 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 6 0.016 0.000
2025-07-27 07:51:50,426 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /mail/thread/messages HTTP/1.1" 200 - 34 0.058 0.063
2025-07-27 07:51:50,506 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /mail/thread/data HTTP/1.1" 200 - 26 0.078 0.123
2025-07-27 07:51:50,506 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "POST /mail/thread/data HTTP/1.1" 200 - 26 0.070 0.113
2025-07-27 07:51:50,870 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 32 0.028 0.537
2025-07-27 07:51:50,967 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 200 - 8 0.002 0.022
2025-07-27 07:51:50,979 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 200 - 8 0.011 0.025
2025-07-27 07:51:50,983 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:51:50] "GET /web/image/res.partner/2/avatar_128 HTTP/1.1" 304 - 8 0.019 0.009
2025-07-27 07:53:12,771 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:12] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.025 0.012
2025-07-27 07:53:12,785 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:12] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.025 0.023
2025-07-27 07:53:12,785 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:12] "POST /mail/thread/data HTTP/1.1" 200 - 15 0.025 0.014
2025-07-27 07:53:13,000 149816 INFO ardano_hr2 odoo.addons.phone_validation.tools.phone_validation: The `phonenumbers` Python module is not installed, contact numbers will not be verified. Please install the `phonenumbers` Python module. 
2025-07-27 07:53:13,012 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_button HTTP/1.1" 200 - 57 0.163 0.106
2025-07-27 07:53:13,155 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/calendar.event/get_views HTTP/1.1" 200 - 43 0.037 0.077
2025-07-27 07:53:13,217 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/calendar.event/check_access_rights HTTP/1.1" 200 - 3 0.005 0.010
2025-07-27 07:53:13,221 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 3 0.004 0.014
2025-07-27 07:53:13,243 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /appointment/appointment_type/get_staff_user_appointment_types HTTP/1.1" 200 - 6 0.027 0.010
2025-07-27 07:53:13,268 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/calendar.filters/search_read HTTP/1.1" 200 - 7 0.021 0.007
2025-07-27 07:53:13,307 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/calendar.event/search_read HTTP/1.1" 200 - 4 0.013 0.009
2025-07-27 07:53:13,334 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /web/dataset/call_kw/res.partner/get_attendee_detail HTTP/1.1" 200 - 3 0.002 0.007
2025-07-27 07:53:13,482 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "GET /web/static/lib/fullcalendar/core/main.css HTTP/1.1" 200 - 0 0.000 0.050
2025-07-27 07:53:13,489 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "GET /web/static/lib/fullcalendar/daygrid/main.css HTTP/1.1" 200 - 0 0.000 0.052
2025-07-27 07:53:13,490 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "GET /web/static/lib/fullcalendar/timegrid/main.css HTTP/1.1" 200 - 0 0.000 0.049
2025-07-27 07:53:13,492 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "GET /web/static/lib/fullcalendar/list/main.css HTTP/1.1" 200 - 0 0.000 0.040
2025-07-27 07:53:13,558 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "POST /onboarding/appointment HTTP/1.1" 200 - 29 0.093 0.101
2025-07-27 07:53:13,905 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:13] "GET /web/static/lib/fullcalendar/core/main.js HTTP/1.1" 200 - 0 0.000 0.481
2025-07-27 07:53:14,081 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/static/lib/fullcalendar/interaction/main.js HTTP/1.1" 200 - 0 0.000 0.145
2025-07-27 07:53:14,231 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/static/lib/fullcalendar/daygrid/main.js HTTP/1.1" 200 - 0 0.000 0.127
2025-07-27 07:53:14,292 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/static/lib/fullcalendar/luxon/main.js HTTP/1.1" 200 - 0 0.000 0.041
2025-07-27 07:53:14,411 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/static/lib/fullcalendar/timegrid/main.js HTTP/1.1" 200 - 0 0.000 0.102
2025-07-27 07:53:14,494 149816 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/static/lib/fullcalendar/list/main.js HTTP/1.1" 200 - 0 0.000 0.062
2025-07-27 07:53:14,576 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/image/onboarding.onboarding/1/panel_background_image HTTP/1.1" 200 - 7 0.010 0.019
2025-07-27 07:53:14,689 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/image/res.partner/9/avatar_128 HTTP/1.1" 200 - 7 0.012 0.041
2025-07-27 07:53:14,695 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:14] "GET /web/image/res.partner/7/avatar_128 HTTP/1.1" 304 - 8 0.016 0.025
2025-07-27 07:53:16,174 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:16] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 4 0.004 0.010
2025-07-27 07:53:16,286 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:16] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 15 0.054 0.023
2025-07-27 07:53:16,324 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:16] "POST /web/dataset/call_kw/hr.recruitment.stage/search_read HTTP/1.1" 200 - 5 0.004 0.009
2025-07-27 07:53:16,430 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:16] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.036 0.016
2025-07-27 07:53:16,431 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:16] "POST /mail/thread/data HTTP/1.1" 200 - 14 0.022 0.027
2025-07-27 07:53:17,742 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:17] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.008 0.007
2025-07-27 07:53:17,748 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:17] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.013 0.011
2025-07-27 07:53:17,782 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:17] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.006 0.008
2025-07-27 07:53:17,833 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:17] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 18 0.039 0.027
2025-07-27 07:53:24,467 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:24] "POST /web/action/load HTTP/1.1" 200 - 14 0.044 0.025
2025-07-27 07:53:24,695 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:24] "POST /web/dataset/call_kw/hr.employee/get_views HTTP/1.1" 200 - 52 0.091 0.119
2025-07-27 07:53:24,753 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:24] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.013 0.012
2025-07-27 07:53:24,884 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:24] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 32 0.069 0.084
2025-07-27 07:53:24,920 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:24] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.004 0.008
2025-07-27 07:53:25,141 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753602804956 HTTP/1.1" 200 - 11 0.028 0.079
2025-07-27 07:53:25,142 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753602804954 HTTP/1.1" 200 - 11 0.033 0.078
2025-07-27 07:53:25,143 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753602804956 HTTP/1.1" 200 - 11 0.038 0.072
2025-07-27 07:53:25,189 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753602804957 HTTP/1.1" 200 - 6 0.020 0.132
2025-07-27 07:53:25,204 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753602804956 HTTP/1.1" 200 - 6 0.040 0.122
2025-07-27 07:53:25,205 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753602804957 HTTP/1.1" 200 - 6 0.031 0.129
2025-07-27 07:53:25,274 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753602804957 HTTP/1.1" 200 - 6 0.035 0.073
2025-07-27 07:53:25,276 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.036 0.059
2025-07-27 07:53:25,284 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.042 0.049
2025-07-27 07:53:25,303 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.029 0.043
2025-07-27 07:53:25,309 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.048 0.047
2025-07-27 07:53:25,318 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.022 0.051
2025-07-27 07:53:25,331 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753602804958 HTTP/1.1" 200 - 6 0.013 0.034
2025-07-27 07:53:25,338 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753602804959 HTTP/1.1" 200 - 6 0.016 0.029
2025-07-27 07:53:25,348 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753602804959 HTTP/1.1" 200 - 6 0.017 0.034
2025-07-27 07:53:25,369 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753602804960 HTTP/1.1" 200 - 6 0.010 0.031
2025-07-27 07:53:25,371 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753602804960 HTTP/1.1" 200 - 6 0.016 0.038
2025-07-27 07:53:25,377 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753602804960 HTTP/1.1" 200 - 6 0.011 0.024
2025-07-27 07:53:25,379 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753602804960 HTTP/1.1" 200 - 6 0.011 0.025
2025-07-27 07:53:25,384 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:25] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753602804960 HTTP/1.1" 200 - 6 0.011 0.022
2025-07-27 07:53:32,461 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:32] "POST /web/action/load HTTP/1.1" 200 - 13 0.050 0.017
2025-07-27 07:53:32,516 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:32] "POST /web/dataset/call_kw/hr.job.category/get_views HTTP/1.1" 200 - 16 0.024 0.016
2025-07-27 07:53:32,550 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:32] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 4 0.004 0.007
2025-07-27 07:53:32,561 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:32] "POST /web/dataset/call_kw/hr.job.category/web_search_read HTTP/1.1" 200 - 6 0.013 0.010
2025-07-27 07:53:34,384 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "POST /web/dataset/call_kw/hr.employee/get_views HTTP/1.1" 200 - 4 0.002 0.032
2025-07-27 07:53:34,447 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.010 0.014
2025-07-27 07:53:34,529 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 24 0.042 0.061
2025-07-27 07:53:34,569 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.005 0.008
2025-07-27 07:53:34,984 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753602814594 HTTP/1.1" 200 - 6 0.022 0.102
2025-07-27 07:53:34,985 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:34] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753602814597 HTTP/1.1" 200 - 6 0.020 0.100
2025-07-27 07:53:35,012 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753602814597 HTTP/1.1" 200 - 6 0.018 0.118
2025-07-27 07:53:35,017 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753602814598 HTTP/1.1" 200 - 6 0.019 0.113
2025-07-27 07:53:35,019 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753602814598 HTTP/1.1" 200 - 6 0.018 0.104
2025-07-27 07:53:35,023 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753602814596 HTTP/1.1" 200 - 6 0.032 0.118
2025-07-27 07:53:35,040 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753602814599 HTTP/1.1" 200 - 6 0.015 0.028
2025-07-27 07:53:35,043 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753602814598 HTTP/1.1" 200 - 6 0.012 0.040
2025-07-27 07:53:35,070 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753602814600 HTTP/1.1" 200 - 6 0.011 0.037
2025-07-27 07:53:35,079 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753602814600 HTTP/1.1" 200 - 6 0.016 0.033
2025-07-27 07:53:35,085 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753602814601 HTTP/1.1" 200 - 6 0.016 0.032
2025-07-27 07:53:35,088 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753602814601 HTTP/1.1" 200 - 6 0.018 0.027
2025-07-27 07:53:35,095 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753602814601 HTTP/1.1" 200 - 6 0.011 0.034
2025-07-27 07:53:35,096 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753602814602 HTTP/1.1" 200 - 6 0.020 0.022
2025-07-27 07:53:35,118 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753602814602 HTTP/1.1" 200 - 6 0.013 0.029
2025-07-27 07:53:35,133 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753602814602 HTTP/1.1" 200 - 6 0.016 0.026
2025-07-27 07:53:35,145 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753602814603 HTTP/1.1" 200 - 6 0.012 0.031
2025-07-27 07:53:35,146 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753602814603 HTTP/1.1" 200 - 6 0.011 0.024
2025-07-27 07:53:35,155 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753602814603 HTTP/1.1" 200 - 6 0.012 0.026
2025-07-27 07:53:35,155 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:35] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753602814603 HTTP/1.1" 200 - 6 0.013 0.037
2025-07-27 07:53:36,587 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:36] "POST /web/action/load HTTP/1.1" 200 - 12 0.027 0.023
2025-07-27 07:53:36,695 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:36] "POST /web/dataset/call_kw/hr.job.grade/get_views HTTP/1.1" 200 - 14 0.043 0.039
2025-07-27 07:53:36,798 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:36] "POST /web/dataset/call_kw/hr.job.grade/web_search_read HTTP/1.1" 200 - 21 0.048 0.017
2025-07-27 07:53:40,723 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:40] "POST /web/action/load HTTP/1.1" 200 - 12 0.013 0.017
2025-07-27 07:53:40,804 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:40] "POST /web/dataset/call_kw/hr.division/get_views HTTP/1.1" 200 - 17 0.021 0.039
2025-07-27 07:53:40,897 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:40] "POST /web/dataset/call_kw/hr.division/web_search_read HTTP/1.1" 200 - 16 0.035 0.023
2025-07-27 07:53:44,780 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:44] "POST /web/action/load HTTP/1.1" 200 - 12 0.009 0.020
2025-07-27 07:53:44,848 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:44] "POST /web/dataset/call_kw/hr.job.equipment/get_views HTTP/1.1" 200 - 17 0.015 0.030
2025-07-27 07:53:44,891 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:44] "POST /web/dataset/call_kw/hr.job.equipment/web_search_read HTTP/1.1" 200 - 5 0.006 0.008
2025-07-27 07:53:46,951 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:46] "POST /web/action/load HTTP/1.1" 200 - 12 0.011 0.015
2025-07-27 07:53:47,004 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:47] "POST /web/dataset/call_kw/hr.unit/get_views HTTP/1.1" 200 - 14 0.010 0.026
2025-07-27 07:53:47,074 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:47] "POST /web/dataset/call_kw/hr.unit/web_search_read HTTP/1.1" 200 - 12 0.036 0.011
2025-07-27 07:53:47,100 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:47] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.006 0.005
2025-07-27 07:53:51,861 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:51] "POST /web/dataset/call_kw/hr.employee/search_panel_select_range HTTP/1.1" 200 - 7 0.007 0.017
2025-07-27 07:53:52,005 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 24 0.048 0.085
2025-07-27 07:53:52,110 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.004 0.014
2025-07-27 07:53:52,347 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753602832145 HTTP/1.1" 200 - 6 0.005 0.025
2025-07-27 07:53:52,390 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753602832150 HTTP/1.1" 200 - 6 0.014 0.020
2025-07-27 07:53:52,401 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753602832147 HTTP/1.1" 200 - 6 0.005 0.018
2025-07-27 07:53:52,416 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753602832148 HTTP/1.1" 200 - 6 0.002 0.021
2025-07-27 07:53:52,439 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753602832151 HTTP/1.1" 200 - 6 0.009 0.017
2025-07-27 07:53:52,452 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753602832152 HTTP/1.1" 200 - 6 0.007 0.018
2025-07-27 07:53:52,479 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753602832153 HTTP/1.1" 200 - 6 0.006 0.020
2025-07-27 07:53:52,491 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753602832153 HTTP/1.1" 200 - 6 0.006 0.018
2025-07-27 07:53:52,518 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753602832154 HTTP/1.1" 200 - 6 0.004 0.019
2025-07-27 07:53:52,539 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753602832155 HTTP/1.1" 200 - 6 0.006 0.021
2025-07-27 07:53:52,553 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753602832156 HTTP/1.1" 200 - 6 0.005 0.015
2025-07-27 07:53:52,574 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753602832157 HTTP/1.1" 200 - 6 0.006 0.014
2025-07-27 07:53:52,597 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753602832157 HTTP/1.1" 200 - 6 0.009 0.012
2025-07-27 07:53:52,612 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753602832158 HTTP/1.1" 200 - 6 0.006 0.016
2025-07-27 07:53:52,636 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753602832158 HTTP/1.1" 200 - 6 0.006 0.019
2025-07-27 07:53:52,644 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753602832159 HTTP/1.1" 200 - 6 0.010 0.011
2025-07-27 07:53:52,659 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753602832159 HTTP/1.1" 200 - 6 0.006 0.018
2025-07-27 07:53:52,670 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753602832160 HTTP/1.1" 200 - 6 0.006 0.014
2025-07-27 07:53:52,684 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753602832160 HTTP/1.1" 200 - 6 0.007 0.019
2025-07-27 07:53:52,688 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:52] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753602832160 HTTP/1.1" 200 - 6 0.006 0.012
2025-07-27 07:53:53,697 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 33 0.050 0.027
2025-07-27 07:53:53,720 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.003 0.004
2025-07-27 07:53:53,749 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.007 0.008
2025-07-27 07:53:53,759 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 8 0.013 0.013
2025-07-27 07:53:53,849 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /hr/get_org_chart HTTP/1.1" 200 - 10 0.016 0.016
2025-07-27 07:53:53,864 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /mail/thread/data HTTP/1.1" 200 - 17 0.023 0.020
2025-07-27 07:53:53,884 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.046 0.018
2025-07-27 07:53:53,916 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:53] "GET /web/image?model=hr.employee&id=17&field=avatar_128&unique=1753543833000 HTTP/1.1" 200 - 6 0.005 0.008
2025-07-27 07:53:55,941 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:55] "POST /web/dataset/call_kw/hr.unit/name_search HTTP/1.1" 200 - 4 0.006 0.010
2025-07-27 07:53:59,042 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:53:59] "POST /web/dataset/call_kw/hr.job/name_search HTTP/1.1" 200 - 5 0.004 0.010
2025-07-27 07:54:02,317 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:02] "POST /web/dataset/call_kw/hr.employee/onchange HTTP/1.1" 200 - 9 0.015 0.021
2025-07-27 07:54:02,379 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:02] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.019 0.017
2025-07-27 07:54:02,381 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:02] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.032 0.009
2025-07-27 07:54:05,589 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:05] "POST /web/dataset/call_kw/hr.unit/name_search HTTP/1.1" 200 - 4 0.006 0.008
2025-07-27 07:54:06,415 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:06] "POST /web/dataset/call_kw/hr.department/name_search HTTP/1.1" 200 - 5 0.005 0.006
2025-07-27 07:54:08,085 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:08] "POST /web/dataset/call_kw/hr.employee/onchange HTTP/1.1" 200 - 7 0.010 0.016
2025-07-27 07:54:08,159 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:08] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.011 0.019
2025-07-27 07:54:08,172 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:08] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.032 0.014
2025-07-27 07:54:09,127 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:09] "POST /web/dataset/call_kw/hr.unit/name_search HTTP/1.1" 200 - 5 0.008 0.009
2025-07-27 07:54:09,745 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:09] "POST /web/dataset/call_kw/hr.unit/read HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 07:54:09,823 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:09] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.034 0.012
2025-07-27 07:54:09,841 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:09] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.041 0.020
2025-07-27 07:54:13,276 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:13] "POST /web/dataset/call_kw/hr.job/name_search HTTP/1.1" 200 - 5 0.006 0.012
2025-07-27 07:54:14,496 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:14] "POST /web/dataset/call_kw/hr.employee/onchange HTTP/1.1" 200 - 14 0.017 0.025
2025-07-27 07:54:14,545 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:14] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.013
2025-07-27 07:54:14,566 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:14] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.020 0.019
2025-07-27 07:54:14,569 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:14] "POST /hr/get_org_chart HTTP/1.1" 200 - 6 0.009 0.020
2025-07-27 07:54:17,629 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:17] "POST /web/dataset/call_kw/hr.department/name_search HTTP/1.1" 200 - 5 0.004 0.006
2025-07-27 07:54:18,383 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:18] "POST /web/dataset/call_kw/hr.employee/onchange HTTP/1.1" 200 - 7 0.008 0.016
2025-07-27 07:54:18,436 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:18] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.008 0.017
2025-07-27 07:54:18,452 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:18] "POST /hr/get_org_chart HTTP/1.1" 200 - 6 0.010 0.018
2025-07-27 07:54:18,463 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:18] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.015 0.029
2025-07-27 07:54:19,379 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:19] "POST /web/dataset/call_kw/hr.unit/name_search HTTP/1.1" 200 - 5 0.009 0.009
2025-07-27 07:54:19,965 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:19] "POST /web/dataset/call_kw/hr.unit/read HTTP/1.1" 200 - 4 0.003 0.007
2025-07-27 07:54:20,012 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:20] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.006 0.017
2025-07-27 07:54:20,031 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:20] "POST /hr/get_org_chart HTTP/1.1" 200 - 6 0.016 0.014
2025-07-27 07:54:20,038 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:20] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.020 0.024
2025-07-27 07:54:32,477 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /mail/thread/messages HTTP/1.1" 200 - 6 0.013 0.016
2025-07-27 07:54:32,509 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.036 0.022
2025-07-27 07:54:32,603 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/hr.employee/write HTTP/1.1" 200 - 33 0.097 0.051
2025-07-27 07:54:32,701 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/hr.employee/read HTTP/1.1" 200 - 35 0.050 0.038
2025-07-27 07:54:32,737 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/hr.unit/read HTTP/1.1" 200 - 4 0.004 0.006
2025-07-27 07:54:32,738 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/hr.division/read HTTP/1.1" 200 - 4 0.003 0.010
2025-07-27 07:54:32,769 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 5 0.005 0.009
2025-07-27 07:54:32,776 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.012 0.010
2025-07-27 07:54:32,846 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.035 0.019
2025-07-27 07:54:32,861 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /hr/get_org_chart HTTP/1.1" 200 - 10 0.030 0.022
2025-07-27 07:54:32,862 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /mail/thread/data HTTP/1.1" 200 - 13 0.047 0.019
2025-07-27 07:54:32,863 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "POST /mail/thread/messages HTTP/1.1" 200 - 23 0.051 0.022
2025-07-27 07:54:32,875 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "GET /web/image?model=hr.employee&id=17&field=avatar_128&unique=1753602872000 HTTP/1.1" 200 - 6 0.010 0.024
2025-07-27 07:54:32,924 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "GET /web/image/hr.employee.public/17/avatar_1024/ HTTP/1.1" 200 - 11 0.012 0.015
2025-07-27 07:54:32,928 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:32] "GET /web/image/hr.employee.public/1/avatar_1024/ HTTP/1.1" 200 - 11 0.014 0.016
2025-07-27 07:54:34,009 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "POST /web/dataset/call_kw/hr.employee/web_search_read HTTP/1.1" 200 - 24 0.025 0.056
2025-07-27 07:54:34,039 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "POST /web/dataset/call_kw/hr.employee.category/read HTTP/1.1" 200 - 4 0.002 0.006
2025-07-27 07:54:34,258 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=20&field=image_1024&unique=1753602874056 HTTP/1.1" 200 - 6 0.024 0.028
2025-07-27 07:54:34,283 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=3&field=image_1024&unique=1753602874057 HTTP/1.1" 200 - 6 0.019 0.053
2025-07-27 07:54:34,305 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=15&field=image_1024&unique=1753602874058 HTTP/1.1" 200 - 6 0.020 0.057
2025-07-27 07:54:34,309 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=9&field=image_1024&unique=1753602874057 HTTP/1.1" 200 - 6 0.030 0.067
2025-07-27 07:54:34,320 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=19&field=image_1024&unique=1753602874058 HTTP/1.1" 200 - 6 0.027 0.072
2025-07-27 07:54:34,356 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=17&field=image_1024&unique=1753602874057 HTTP/1.1" 200 - 6 0.037 0.080
2025-07-27 07:54:34,362 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=18&field=image_1024&unique=1753602874058 HTTP/1.1" 200 - 6 0.024 0.064
2025-07-27 07:54:34,394 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=11&field=image_1024&unique=1753602874059 HTTP/1.1" 200 - 6 0.023 0.069
2025-07-27 07:54:34,411 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=6&field=image_1024&unique=1753602874059 HTTP/1.1" 200 - 6 0.024 0.060
2025-07-27 07:54:34,418 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=8&field=image_1024&unique=1753602874059 HTTP/1.1" 200 - 6 0.017 0.059
2025-07-27 07:54:34,435 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=7&field=image_1024&unique=1753602874059 HTTP/1.1" 200 - 6 0.028 0.059
2025-07-27 07:54:34,451 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=1&field=image_1024&unique=1753602874061 HTTP/1.1" 200 - 6 0.024 0.061
2025-07-27 07:54:34,471 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=14&field=image_1024&unique=1753602874061 HTTP/1.1" 200 - 6 0.034 0.059
2025-07-27 07:54:34,490 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=16&field=image_1024&unique=1753602874062 HTTP/1.1" 200 - 6 0.026 0.053
2025-07-27 07:54:34,500 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=5&field=image_1024&unique=1753602874062 HTTP/1.1" 200 - 6 0.024 0.037
2025-07-27 07:54:34,501 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=2&field=image_1024&unique=1753602874062 HTTP/1.1" 200 - 6 0.020 0.036
2025-07-27 07:54:34,519 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=12&field=image_1024&unique=1753602874064 HTTP/1.1" 200 - 6 0.012 0.030
2025-07-27 07:54:34,520 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=4&field=image_1024&unique=1753602874064 HTTP/1.1" 200 - 6 0.016 0.040
2025-07-27 07:54:34,525 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=10&field=image_1024&unique=1753602874064 HTTP/1.1" 200 - 6 0.014 0.029
2025-07-27 07:54:34,528 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:34] "GET /web/image?model=hr.employee&id=13&field=image_1024&unique=1753602874065 HTTP/1.1" 200 - 6 0.011 0.020
2025-07-27 07:54:37,181 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:37] "POST /web/action/load HTTP/1.1" 200 - 17 0.039 0.035
2025-07-27 07:54:37,340 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:37] "POST /web/dataset/call_kw/hr.job/get_views HTTP/1.1" 200 - 45 0.043 0.089
2025-07-27 07:54:37,391 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:37] "POST /web/dataset/call_kw/hr.job/search_panel_select_range HTTP/1.1" 200 - 7 0.014 0.014
2025-07-27 07:54:37,425 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:37] "POST /web/dataset/call_kw/hr.job/web_search_read HTTP/1.1" 200 - 15 0.025 0.035
2025-07-27 07:54:51,083 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:51] "POST /web/dataset/call_kw/hr.job/read HTTP/1.1" 200 - 37 0.076 0.037
2025-07-27 07:54:51,113 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:51] "POST /web/dataset/call_kw/hr.job.access/read HTTP/1.1" 200 - 4 0.003 0.008
2025-07-27 07:54:51,147 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:51] "POST /web/dataset/call_kw/res.partner/name_get HTTP/1.1" 200 - 6 0.008 0.011
2025-07-27 07:54:51,248 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:51] "POST /mail/thread/data HTTP/1.1" 200 - 18 0.022 0.017
2025-07-27 07:54:51,294 149816 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:54:51] "POST /mail/thread/messages HTTP/1.1" 200 - 32 0.054 0.033
