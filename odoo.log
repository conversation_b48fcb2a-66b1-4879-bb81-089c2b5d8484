2025-07-27 07:31:37,650 150236 INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:31:37,654 150236 INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-07-27 07:31:37,665 150236 INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:31:37,709 150236 INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:31:38,030 150236 INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:31:38,238 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:31:38,275 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:31:38,280 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:31:38,288 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:31:38,291 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:31:38,295 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:31:38,297 150236 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:31:38,298 150236 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 808, in convert_xml_import
    doc = etree.parse(xmlfile)
          ^^^^^^^^^^^^^^^^^^^^
  File "src\\lxml\\etree.pyx", line 3570, in lxml.etree.parse
  File "src\\lxml\\parser.pxi", line 1973, in lxml.etree._parseDocument
  File "src\\lxml\\parser.pxi", line 1993, in lxml.etree._parseFilelikeDocument
  File "src\\lxml\\parser.pxi", line 1887, in lxml.etree._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 1224, in lxml.etree._BaseParser._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 633, in lxml.etree._ParserContext._handleParseResultDoc
  File "src\\lxml\\parser.pxi", line 743, in lxml.etree._handleParseResult
  File "src\\lxml\\parser.pxi", line 672, in lxml.etree._raiseParseError
  File "c:\odoo16\server\custom\alwasead_job_position_database\data\recruitment_stages_data.xml", line 54
lxml.etree.XMLSyntaxError: Double hyphen within comment: <!-- <record id="hr_recruitment.stage_job2" model="hr., line 54, column 11
2025-07-27 07:31:38,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 808, in convert_xml_import
    doc = etree.parse(xmlfile)
          ^^^^^^^^^^^^^^^^^^^^
  File "src\\lxml\\etree.pyx", line 3570, in lxml.etree.parse
  File "src\\lxml\\parser.pxi", line 1973, in lxml.etree._parseDocument
  File "src\\lxml\\parser.pxi", line 1993, in lxml.etree._parseFilelikeDocument
  File "src\\lxml\\parser.pxi", line 1887, in lxml.etree._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 1224, in lxml.etree._BaseParser._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 633, in lxml.etree._ParserContext._handleParseResultDoc
  File "src\\lxml\\parser.pxi", line 743, in lxml.etree._handleParseResult
  File "src\\lxml\\parser.pxi", line 672, in lxml.etree._raiseParseError
  File "c:\odoo16\server\custom\alwasead_job_position_database\data\recruitment_stages_data.xml", line 54
lxml.etree.XMLSyntaxError: Double hyphen within comment: <!-- <record id="hr_recruitment.stage_job2" model="hr., line 54, column 11
2025-07-27 07:31:38,********** INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:31:38] "GET /web HTTP/1.1" 500 - 255 0.223 0.437
2025-07-27 07:31:42,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:31:42,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:31:42,********** INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:31:42,********** INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:31:42,********** INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:31:42,********** INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:31:42,********** INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:31:42,642 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:31:42,645 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:31:42,649 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:31:42,652 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:31:42,654 150236 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:31:42,654 150236 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 808, in convert_xml_import
    doc = etree.parse(xmlfile)
          ^^^^^^^^^^^^^^^^^^^^
  File "src\\lxml\\etree.pyx", line 3570, in lxml.etree.parse
  File "src\\lxml\\parser.pxi", line 1973, in lxml.etree._parseDocument
  File "src\\lxml\\parser.pxi", line 1993, in lxml.etree._parseFilelikeDocument
  File "src\\lxml\\parser.pxi", line 1887, in lxml.etree._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 1224, in lxml.etree._BaseParser._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 633, in lxml.etree._ParserContext._handleParseResultDoc
  File "src\\lxml\\parser.pxi", line 743, in lxml.etree._handleParseResult
  File "src\\lxml\\parser.pxi", line 672, in lxml.etree._raiseParseError
  File "c:\odoo16\server\custom\alwasead_job_position_database\data\recruitment_stages_data.xml", line 54
lxml.etree.XMLSyntaxError: Double hyphen within comment: <!-- <record id="hr_recruitment.stage_job2" model="hr., line 54, column 11
2025-07-27 07:31:42,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 232, in load_module_graph
    load_data(cr, idref, mode, kind='data', package=package)
  File "C:\odoo16\server\odoo\modules\loading.py", line 72, in load_data
    tools.convert_file(cr, package.name, filename, idref, mode, noupdate, kind)
  File "C:\odoo16\server\odoo\tools\convert.py", line 763, in convert_file
    convert_xml_import(cr, module, fp, idref, mode, noupdate)
  File "C:\odoo16\server\odoo\tools\convert.py", line 808, in convert_xml_import
    doc = etree.parse(xmlfile)
          ^^^^^^^^^^^^^^^^^^^^
  File "src\\lxml\\etree.pyx", line 3570, in lxml.etree.parse
  File "src\\lxml\\parser.pxi", line 1973, in lxml.etree._parseDocument
  File "src\\lxml\\parser.pxi", line 1993, in lxml.etree._parseFilelikeDocument
  File "src\\lxml\\parser.pxi", line 1887, in lxml.etree._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 1224, in lxml.etree._BaseParser._parseDocFromFilelike
  File "src\\lxml\\parser.pxi", line 633, in lxml.etree._ParserContext._handleParseResultDoc
  File "src\\lxml\\parser.pxi", line 743, in lxml.etree._handleParseResult
  File "src\\lxml\\parser.pxi", line 672, in lxml.etree._raiseParseError
  File "c:\odoo16\server\custom\alwasead_job_position_database\data\recruitment_stages_data.xml", line 54
lxml.etree.XMLSyntaxError: Double hyphen within comment: <!-- <record id="hr_recruitment.stage_job2" model="hr., line 54, column 11
2025-07-27 07:31:42,********** INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:31:42] "GET /websocket HTTP/1.1" 500 - 255 0.216 0.226
2025-07-27 07:31:57,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-07-27 07:31:57,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:31:57,********** INFO ? odoo.modules.loading: loading 89 modules... 
2025-07-27 07:31:57,********** INFO ? odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:31:57,********** INFO ? odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:31:57,********** INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:31:57,********** INFO ? odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:31:57,729 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:31:57,736 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:31:57,740 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:31:57,745 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:31:57,747 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:31:57,753 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:31:57,760 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:31:57,763 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:31:57,763 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:31:57,778 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:31:57,781 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:31:57,784 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:31:57,797 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:31:57,797 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:31:57,805 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:31:57,873 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:31:57,918 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:31:57,934 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:31:57,950 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:31:57,970 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:31:57,988 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:31:58,124 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:31:58,210 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:31:58,326 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:31:58,356 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:31:58,385 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:31:58,398 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:31:58,421 150236 INFO ? odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:31:58,606 150236 WARNING ? odoo.modules.loading: The models ['hr.applicant.additional.info.wizard', 'hr.applicant.rejection.wizard'] have no access rules in module alwasead_job_position_database, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
alwasead_job_position_database.access_hr_applicant_additional_info_wizard,access_hr_applicant_additional_info_wizard,alwasead_job_position_database.model_hr_applicant_additional_info_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_rejection_wizard,access_hr_applicant_rejection_wizard,alwasead_job_position_database.model_hr_applicant_rejection_wizard,base.group_user,1,0,0,0 
2025-07-27 07:31:58,611 150236 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-07-27 07:31:58,612 150236 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'ardano_hr2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 305, in load_module_graph
    module.write({'state': 'installed', 'latest_version': ver})
  File "C:\odoo16\server\odoo\addons\website\models\ir_module_module.py", line 78, in write
    if request and request.db and request.context.get('apply_new_theme'):
                                  ^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1291, in context
    return self.env.context
           ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'context'
2025-07-27 07:31:58,671 150236 INFO ? werkzeug: 127.0.0.1 - - [27/Jul/2025 07:31:58] "GET /websocket HTTP/1.1" 404 - 897 0.694 0.755
2025-07-27 07:32:11,295 136444 INFO ? odoo: Odoo version 16.0-20250210 
2025-07-27 07:32:11,296 136444 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-07-27 07:32:11,296 136444 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\custom'] 
2025-07-27 07:32:11,296 136444 INFO ? odoo: database: openpg@localhost:5432 
2025-07-27 07:32:11,463 136444 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-07-27 07:32:11,675 136444 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8090 
2025-07-27 07:32:11,732 136444 INFO ardano_hr2 odoo.modules.loading: loading 1 modules... 
2025-07-27 07:32:11,737 136444 INFO ardano_hr2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-27 07:32:11,775 136444 INFO ardano_hr2 odoo.modules.loading: updating modules list 
2025-07-27 07:32:11,778 136444 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-07-27 07:32:12,959 136444 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:32:12,959 136444 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['AlWasead Job Position Database'] to user __system__ #1 via n/a 
2025-07-27 07:32:13,829 136444 INFO ardano_hr2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-07-27 07:32:13,880 136444 INFO ardano_hr2 odoo.modules.loading: loading 89 modules... 
2025-07-27 07:32:13,900 136444 WARNING ardano_hr2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-07-27 07:32:14,448 136444 INFO ardano_hr2 odoo.modules.loading: Loading module alwasead_job_position_database (89/89) 
2025-07-27 07:32:14,523 136444 WARNING ardano_hr2 odoo.api.create: The model odoo.addons.alwasead_job_position_database.models.hr_applicant is not overriding the create method in batch 
2025-07-27 07:32:14,583 136444 INFO ardano_hr2 odoo.modules.registry: module alwasead_job_position_database: creating or updating database tables 
2025-07-27 07:32:14,722 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/ir.model.access.csv 
2025-07-27 07:32:14,754 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/security/security.xml 
2025-07-27 07:32:14,759 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_categories_data.xml 
2025-07-27 07:32:14,766 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/job_grades_data.xml 
2025-07-27 07:32:14,771 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/alwasead_divisions_data.xml 
2025-07-27 07:32:14,775 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_data.xml 
2025-07-27 07:32:14,778 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_staffing_simple.xml 
2025-07-27 07:32:14,783 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_approval.xml 
2025-07-27 07:32:14,785 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_jva_notifications.xml 
2025-07-27 07:32:14,787 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_confirmation.xml 
2025-07-27 07:32:14,793 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_initial_qualification.xml 
2025-07-27 07:32:14,798 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/email_template_application_rejection.xml 
2025-07-27 07:32:14,803 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/recruitment_stages_email_templates.xml 
2025-07-27 07:32:14,806 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/website_form_whitelist.xml 
2025-07-27 07:32:14,816 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_sequence.xml 
2025-07-27 07:32:14,819 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/data/ir_cron.xml 
2025-07-27 07:32:14,822 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_views.xml 
2025-07-27 07:32:14,878 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_jva_form_views.xml 
2025-07-27 07:32:14,902 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_category_views.xml 
2025-07-27 07:32:14,912 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_grade_views.xml 
2025-07-27 07:32:14,923 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_division_views.xml 
2025-07-27 07:32:14,944 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_equipment_views.xml 
2025-07-27 07:32:14,958 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_job_access_views.xml 
2025-07-27 07:32:14,973 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_employee_views.xml 
2025-07-27 07:32:15,010 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/hr_applicant_views.xml 
2025-07-27 07:32:15,052 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/website_hr_recruitment_templates.xml 
2025-07-27 07:32:15,063 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/hr_applicant_wizard_views.xml 
2025-07-27 07:32:15,071 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/wizard/jva_reject_wizard_views.xml 
2025-07-27 07:32:15,076 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/reports/job_position_report.xml 
2025-07-27 07:32:15,093 136444 INFO ardano_hr2 odoo.modules.loading: loading alwasead_job_position_database/views/menu.xml 
2025-07-27 07:32:15,162 136444 WARNING ardano_hr2 odoo.modules.loading: The models ['hr.applicant.additional.info.wizard', 'hr.applicant.rejection.wizard'] have no access rules in module alwasead_job_position_database, consider adding some, like:
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
alwasead_job_position_database.access_hr_applicant_additional_info_wizard,access_hr_applicant_additional_info_wizard,alwasead_job_position_database.model_hr_applicant_additional_info_wizard,base.group_user,1,0,0,0
alwasead_job_position_database.access_hr_applicant_rejection_wizard,access_hr_applicant_rejection_wizard,alwasead_job_position_database.model_hr_applicant_rejection_wizard,base.group_user,1,0,0,0 
2025-07-27 07:32:15,171 136444 INFO ardano_hr2 odoo.modules.loading: Module alwasead_job_position_database loaded in 0.72s, 902 queries (+902 other) 
2025-07-27 07:32:15,171 136444 INFO ardano_hr2 odoo.modules.loading: 89 modules loaded in 1.29s, 902 queries (+902 extra) 
2025-07-27 07:32:15,599 136444 INFO ardano_hr2 odoo.modules.loading: Modules loaded. 
2025-07-27 07:32:15,605 136444 INFO ardano_hr2 odoo.modules.registry: Registry loaded in 3.934s 
2025-07-27 07:32:15,740 136444 INFO ardano_hr2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-07-27 07:32:17,452 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:17] "GET /web HTTP/1.1" 200 - 178 0.136 5.442
2025-07-27 07:32:18,191 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "GET /websocket HTTP/1.1" 101 - 2 0.009 0.083
2025-07-27 07:32:18,243 136444 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-07-27 07:32:18,319 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "POST /mail/init_messaging HTTP/1.1" 200 - 57 0.128 0.098
2025-07-27 07:32:18,337 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.008 0.089
2025-07-27 07:32:18,359 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.009 0.011
2025-07-27 07:32:18,455 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "POST /web/action/load HTTP/1.1" 200 - 14 0.009 0.384
2025-07-27 07:32:18,562 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 38 0.037 0.321
2025-07-27 07:32:18,569 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "POST /web/dataset/call_kw/hr.applicant/get_views HTTP/1.1" 200 - 64 0.029 0.075
2025-07-27 07:32:18,607 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "POST /web/dataset/call_kw/hr.job/name_get HTTP/1.1" 200 - 8 0.004 0.004
2025-07-27 07:32:18,626 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 7 0.004 0.004
2025-07-27 07:32:18,626 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.006 0.002
2025-07-27 07:32:18,644 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.002 0.003
2025-07-27 07:32:18,656 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 18 0.011 0.008
2025-07-27 07:32:18,773 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:18] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 304 - 8 0.014 0.013
2025-07-27 07:32:19,710 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:19] "POST /web/dataset/call_kw/hr.applicant/read HTTP/1.1" 200 - 17 0.045 0.022
2025-07-27 07:32:20,009 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:20] "POST /mail/thread/messages HTTP/1.1" 200 - 30 0.040 0.141
2025-07-27 07:32:20,010 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:20] "POST /mail/thread/data HTTP/1.1" 200 - 32 0.048 0.133
2025-07-27 07:32:20,019 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:20] "POST /web/dataset/call_kw/hr.applicant/read_progress_bar HTTP/1.1" 200 - 4 0.007 0.172
2025-07-27 07:32:20,023 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:20] "POST /web/dataset/call_kw/hr.applicant/web_read_group HTTP/1.1" 200 - 6 0.009 0.176
2025-07-27 07:32:20,050 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:20] "POST /web/dataset/call_kw/hr.recruitment.stage/read HTTP/1.1" 200 - 4 0.003 0.008
2025-07-27 07:32:20,082 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:20] "POST /web/dataset/call_kw/hr.applicant/web_search_read HTTP/1.1" 200 - 16 0.027 0.017
2025-07-27 07:32:20,147 136444 INFO ardano_hr2 werkzeug: 127.0.0.1 - - [27/Jul/2025 07:32:20] "GET /web/bundle/web_editor.assets_wysiwyg?lang=en_US&debug=1 HTTP/1.1" 200 - 32 0.025 0.292
