# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import timedelta


class HrJob(models.Model):
    _inherit = 'hr.job'

    # Job Information Section (معلومات عن الوظيفة)
    division_id = fields.Many2one('hr.division', string='Division (القسم)')
    unit_id = fields.Many2one('hr.unit', string='Unit/Team (الوحدة/الفريق)')
    required_positions = fields.Integer(
        string='Number of Positions in Team (عدد المناصب في الفريق)',
        default=1,
        help="Target number of employees for this position"
    )
    supervisor_id = fields.Many2one(
        'hr.employee', 
        string='Direct Supervisor (المشرف المباشر)',
        help="Direct supervisor for this position"
    )
    place_of_work = fields.Selection([
        ('office', 'Office (المكتب)'),
        ('site', 'Site (الموقع)')
    ], string='Place of Work (مكان العمل)', default='office')

    # Job Description Section (الوصف الوظيفي)
    job_overview = fields.Text(
        string='Job Overview (نظرة عامة على الوظيفة)',
        help="One clear paragraph summarizing the primary mission of the job"
    )
    key_responsibilities = fields.Text(
        string='Key Responsibilities (المسؤوليات الرئيسية)',
        help="List of 6-12 specific tasks and outcomes"
    )
    key_competencies = fields.Text(
        string='Key Competencies (الكفاءات الرئيسية)',
        help="Key skills for the position to perform"
    )
    authorities_decisions = fields.Text(
        string='Authorities and Decision Rights (السلطات وحقوق اتخاذ القرار)',
        help="What the position can decide or approve"
    )
    education_background = fields.Text(
        string='Required Educational Background (الخلفية التعليمية المطلوبة)',
        help="Degrees, certifications, special licenses"
    )
    technical_skills = fields.Text(
        string='Required Technical Skills (المهارات التقنية المطلوبة)',
        help="Software, tools, procedures, standards"
    )
    kpis = fields.Text(
        string='Key Performance Indicators (مؤشرات الأداء الرئيسية)',
        help="KPIs for measuring performance"
    )
    working_conditions = fields.Text(
        string='Working Conditions/Environment/Risk (ظروف العمل/البيئة/المخاطر)',
        help="Working conditions, environment, and risk exposure"
    )

    # Job Classification
    job_category_id = fields.Many2one(
        'hr.job.category',
        string='Job Category (فئة الوظيفة)',
        help="Used for candidate filtering based on education requirements"
    )
    job_grade_id = fields.Many2one(
        'hr.job.grade',
        string='Job Grade (درجة الوظيفة)',
        help="Used for salary calculation and career progression"
    )

    # Assets and Access (الأصول والتصاريح المحمية)
    equipment_ids = fields.One2many(
        'hr.job.equipment', 
        'job_id', 
        string='Required Equipment (المعدات المطلوبة)'
    )
    access_ids = fields.One2many(
        'hr.job.access', 
        'job_id', 
        string='Software/Platform Access (الوصول للبرمجيات/المنصات)'
    )

    # Staffing Level Tracking
    current_employees = fields.Integer(
        string='Current Employees',
        compute='_compute_current_employees',
        store=True
    )
    vacant_positions = fields.Integer(
        string='Vacant Positions',
        compute='_compute_vacant_positions',
        store=True
    )
    staffing_status = fields.Selection([
        ('adequate', 'Adequately Staffed'),
        ('understaffed', 'Understaffed'),
        ('overstaffed', 'Overstaffed')
    ], string='Staffing Status', compute='_compute_staffing_status', store=True)

    # Approval Tracking (تتبع الموافقات)
    prepared_by = fields.Many2one(
        'res.users', 
        string='Prepared By (من إعداد)', 
        default=lambda self: self.env.user,
        readonly=True
    )
    prepared_date = fields.Datetime(
        string='Prepared Date', 
        default=fields.Datetime.now,
        readonly=True
    )
    approved_by_dept_head = fields.Many2one(
        'hr.employee', 
        string='Approved by Head of Department (رئيس القسم)'
    )
    approved_by_division_head = fields.Many2one(
        'hr.employee', 
        string='Approved by Head of Division (رئيس القسم/المكتب)'
    )
    approved_by_general_manager = fields.Many2one(
        'hr.employee', 
        string='Approved by General Manager (المدير العام)'
    )
    approval_state = fields.Selection([
        ('draft', 'Draft'),
        ('dept_approved', 'Department Head Approved'),
        ('division_approved', 'Division Head Approved'),
        ('final_approved', 'General Manager Approved')
    ], string='Approval Status', default='draft')

    # JVA Related
    jva_form_ids = fields.One2many(
        'hr.jva.form', 
        'job_id', 
        string='JVA Forms'
    )
    active_jva_count = fields.Integer(
        string='Active JVA Forms',
        compute='_compute_active_jva_count'
    )

    @api.depends('employee_ids', 'employee_ids.active')
    def _compute_current_employees(self):
        for job in self:
            job.current_employees = len(job.employee_ids.filtered('active'))

    @api.depends('expected_employees', 'no_of_employee')
    def _compute_vacant_positions(self):
        for job in self:
            job.vacant_positions = max(0, job.expected_employees - job.no_of_employee)

    @api.depends('expected_employees', 'no_of_employee')
    def _compute_staffing_status(self):
        for job in self:
            if job.no_of_employee < job.expected_employees:
                job.staffing_status = 'understaffed'
            elif job.no_of_employee > job.expected_employees:
                job.staffing_status = 'overstaffed'
            else:
                job.staffing_status = 'adequate'

    @api.depends('jva_form_ids', 'jva_form_ids.state')
    def _compute_active_jva_count(self):
        for job in self:
            job.active_jva_count = len(job.jva_form_ids.filtered(
                lambda jva: jva.state in ('draft', 'approval_pending', 'approved')
            ))

    def action_create_jva_form(self):
        """Create new JVA form for this job position"""
        if self.staffing_status != 'understaffed':
            raise UserError(_('JVA forms can only be created for understaffed positions.'))

        return {
            'type': 'ir.actions.act_window',
            'name': _('Create JVA Form'),
            'res_model': 'hr.jva.form',
            'view_mode': 'form',
            'target': 'current',
            'context': {
                'default_job_id': self.id,
                'default_requested_positions': self.no_of_recruitment,
            }
        }

    def action_view_jva_forms(self):
        """View all JVA forms for this job position"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('JVA Forms'),
            'res_model': 'hr.jva.form',
            'view_mode': 'tree,form',
            'domain': [('job_id', '=', self.id)],
            'context': {'default_job_id': self.id}
        }

    @api.model
    def _cron_check_staffing_levels(self):
        """Scheduled action to check staffing levels and send notifications"""
        understaffed_jobs = self.search([('staffing_status', '=', 'understaffed')])
        
        for job in understaffed_jobs:
            # Check if notification was already sent recently
            recent_jva = job.jva_form_ids.filtered(
                lambda jva: jva.create_date >= fields.Datetime.now() - timedelta(days=7)
            )
            
            if not recent_jva and job.department_id.manager_id:
                # Send notification to department head
                job._send_staffing_notification()

    def _send_staffing_notification(self):
        """Send notification to department head about staffing gap"""
        if not self.department_id.manager_id:
            return

        try:
            template = self.env.ref('alwasead_job_position_database.email_template_staffing_notification')
            if template:
                template.send_mail(self.id, force_send=True)
        except ValueError:
            # Template doesn't exist, send a simple email instead
            self._send_simple_staffing_notification()

    def _send_simple_staffing_notification(self):
        """Send a simple email notification when template is not available"""
        if not self.department_id.manager_id or not self.department_id.manager_id.work_email:
            return

        subject = f"Staffing Alert: {self.name} is Understaffed"
        body = f"""
        <p>Dear {self.department_id.manager_id.name or 'Manager'},</p>

        <h3 style="color: orange;">Staffing Level Alert</h3>

        <p>The job position <strong>{self.name}</strong> in department <strong>{self.department_id.name}</strong> is currently understaffed.</p>

        <table border="1" style="border-collapse: collapse; width: 100%; margin: 20px 0;">
            <tr>
                <td style="padding: 8px; background-color: #f5f5f5;"><strong>Job Position:</strong></td>
                <td style="padding: 8px;">{self.name}</td>
            </tr>
            <tr>
                <td style="padding: 8px; background-color: #f5f5f5;"><strong>Department:</strong></td>
                <td style="padding: 8px;">{self.department_id.name}</td>
            </tr>
            <tr>
                <td style="padding: 8px; background-color: #f5f5f5;"><strong>Required Positions:</strong></td>
                <td style="padding: 8px;">{self.required_positions}</td>
            </tr>
            <tr>
                <td style="padding: 8px; background-color: #f5f5f5;"><strong>Current Employees:</strong></td>
                <td style="padding: 8px;">{self.current_employees}</td>
            </tr>
            <tr>
                <td style="padding: 8px; background-color: #f5f5f5;"><strong>Vacant Positions:</strong></td>
                <td style="padding: 8px;">{self.vacant_positions}</td>
            </tr>
        </table>

        <p>Consider creating a JVA form to request approval for recruitment.</p>

        <p>Best regards,<br/>
        AlWasead HR System</p>
        """

        mail_values = {
            'subject': subject,
            'body_html': body,
            'email_to': self.department_id.manager_id.work_email,
            'email_from': self.env.user.email or '<EMAIL>',
        }

        mail = self.env['mail.mail'].create(mail_values)
        mail.send()

    @api.constrains('required_positions')
    def _check_required_positions(self):
        for job in self:
            if job.required_positions < 1:
                raise ValidationError(_('Number of positions in team must be at least 1.'))

    # ==========================================
    # APPROVAL WORKFLOW METHODS
    # ==========================================

    def action_submit_for_dept_approval(self):
        """Submit job position for department head approval"""
        self.ensure_one()
        self.write({
            'approval_state': 'dept_approved',
            'approved_by_dept_head': self.env.user.employee_id.id if self.env.user.employee_id else False,
            'dept_approval_date': fields.Date.today(),
        })
        self.message_post(
            body=_('Job position submitted for department head approval by %s') % self.env.user.name,
            message_type='notification'
        )

    def action_submit_for_division_approval(self):
        """Submit job position for division head approval"""
        self.ensure_one()
        if self.approval_state != 'dept_approved':
            raise UserError(_('Job position must be approved by department head first.'))

        self.write({
            'approval_state': 'division_approved',
            'approved_by_division_head': self.env.user.employee_id.id if self.env.user.employee_id else False,
            'division_approval_date': fields.Date.today(),
        })
        self.message_post(
            body=_('Job position approved by division head: %s') % self.env.user.name,
            message_type='notification'
        )

    def action_submit_for_final_approval(self):
        """Submit job position for general manager approval"""
        self.ensure_one()
        if self.approval_state != 'division_approved':
            raise UserError(_('Job position must be approved by division head first.'))

        self.write({
            'approval_state': 'final_approved',
            'approved_by_general_manager': self.env.user.employee_id.id if self.env.user.employee_id else False,
            'general_manager_approval_date': fields.Date.today(),
        })
        self.message_post(
            body=_('Job position finally approved by general manager: %s') % self.env.user.name,
            message_type='notification'
        )

    def action_reset_to_draft(self):
        """Reset job position back to draft"""
        self.ensure_one()
        self.write({
            'approval_state': 'draft',
            'approved_by_dept_head': False,
            'approved_by_division_head': False,
            'approved_by_general_manager': False,
            'dept_approval_date': False,
            'division_approval_date': False,
            'general_manager_approval_date': False,
        })
        self.message_post(
            body=_('Job position reset to draft by %s') % self.env.user.name,
            message_type='notification'
        )
