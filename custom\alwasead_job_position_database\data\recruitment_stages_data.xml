<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- AlWasead Custom Recruitment Stages -->
        <!-- These stages replace the custom qualification_state field -->
        
        <!-- Initial Qualification Stage -->
        <record id="stage_initial_qualification" model="hr.recruitment.stage">
            <field name="name">Initial Qualification</field>
            <field name="sequence">5</field>
            <field name="requirements">Review candidate's basic qualifications, documents, and initial screening</field>
            <field name="fold" eval="False"/>
        </record>
        
        <!-- Sent for Screening Stage -->
        <record id="stage_sent_for_screening" model="hr.recruitment.stage">
            <field name="name">Sent for Screening</field>
            <field name="sequence">10</field>
            <field name="requirements">Application sent to technical interviewer for detailed screening</field>
            <field name="fold" eval="False"/>
        </record>

        <!-- Additional Info Requested Stage -->
        <record id="stage_additional_info_requested" model="hr.recruitment.stage">
            <field name="name">Additional Info Requested</field>
            <field name="sequence">15</field>
            <field name="requirements">Additional information or documents requested from candidate</field>
            <field name="fold" eval="False"/>
        </record>

        <!-- Rejected at Initial Qualification Stage -->
        <record id="stage_rejected_initial" model="hr.recruitment.stage">
            <field name="name">Rejected at Initial Qualification</field>
            <field name="sequence">95</field>
            <field name="requirements">Application rejected during initial qualification phase</field>
            <field name="fold" eval="True"/>
        </record>

        <!-- Initial Qualification Completed Stage -->
        <record id="stage_initial_qualification_completed" model="hr.recruitment.stage">
            <field name="name">Initial Qualification Completed</field>
            <field name="sequence">20</field>
            <field name="requirements">Initial qualification phase completed successfully, ready for next phase</field>
            <field name="fold" eval="False"/>
        </record>
        
        <!-- Update existing standard stages to have proper sequence -->
        <!-- First Interview -->
        <record id="hr_recruitment.stage_job2" model="hr.recruitment.stage">
            <field name="sequence">25</field>
        </record>
        
        <!-- Second Interview -->
        <record id="hr_recruitment.stage_job3" model="hr.recruitment.stage">
            <field name="sequence">30</field>
        </record>
        
        <!-- Contract Proposal -->
        <record id="hr_recruitment.stage_job4" model="hr.recruitment.stage">
            <field name="sequence">35</field>
        </record>
        
        <!-- Contract Signed -->
        <record id="hr_recruitment.stage_job5" model="hr.recruitment.stage">
            <field name="sequence">40</field>
        </record>
        
    </data>
</odoo>
