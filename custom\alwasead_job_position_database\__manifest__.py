# -*- coding: utf-8 -*-
{
    'name': 'AlWasead Job Position Database',
    'version': '********.0',
    'category': 'Human Resources',
    'summary': 'Comprehensive Job Position Database system for AlWasead with automated JVA workflow',
    'description': """
AlWasead Job Position Database
=============================

This module provides a comprehensive Job Position Database system for AlWasead company that includes:

Features:
---------
* Enhanced Job Description Forms with organizational hierarchy
* Automated staffing level monitoring and notifications
* Job Vacancy Announcement (JVA) forms with approval workflow
* Job Categories and Grades system for recruitment and salary calculation
* Equipment and access credentials management
* Location-based salary factor calculations
* Standalone implementation with no external dependencies
* Bilingual support (Arabic/English)

Key Components:
--------------
* Job Position Database with comprehensive fields
* Organizational structure (Division → Department → Unit/Team)
* Automated JVA approval workflow (Department Head → General Manager)
* Job Categories for candidate filtering (Engineer, Financial Officer, etc.)
* Job Grades for salary calculation (Trainee to Lead Consultant)
* Equipment and software access tracking
* Automated notifications and workflow triggers

Technical Implementation:
------------------------
* Extends existing hr.job, hr.employee, hr.recruitment models
* Self-contained approval workflow system
* Built-in email notification system
* Complete security and access control system
    """,
    'author': 'AlWasead',
    'website': 'https://www.alwasead.com',
    'depends': [
        'base',
        'hr',
        'hr_recruitment',
        'website_hr_recruitment',
        'mail',
        'web',
    ],
    'data': [
        # Security
        'security/ir.model.access.csv',
        'security/security.xml',
        
        # Data
        'data/job_categories_data.xml',
        'data/job_grades_data.xml',
        'data/alwasead_divisions_data.xml',
        'data/recruitment_stages_data.xml',
        'data/email_template_staffing_simple.xml',
        'data/email_template_jva_approval.xml',
        'data/email_template_jva_notifications.xml',
        'data/email_template_application_confirmation.xml',
        'data/email_template_initial_qualification.xml',
        'data/email_template_application_rejection.xml',
        'data/website_form_whitelist.xml',
        'data/ir_sequence.xml',
        'data/ir_cron.xml',
        
        # Views
        'views/hr_job_views.xml',
        'views/hr_jva_form_views.xml',
        'views/hr_job_category_views.xml',
        'views/hr_job_grade_views.xml',
        'views/hr_division_views.xml',
        'views/hr_job_equipment_views.xml',
        'views/hr_job_access_views.xml',
        'views/hr_employee_views.xml',
        'views/hr_applicant_views.xml',
        'views/website_hr_recruitment_templates.xml',

        # Wizard views
        'wizard/hr_applicant_wizard_views.xml',
        
        # Wizards
        'wizard/jva_reject_wizard_views.xml',

        # Reports
        'reports/job_position_report.xml',

        # Menu
        'views/menu.xml',
        
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
    'license': 'LGPL-3',
    'sequence': 10,
    'post_init_hook': 'post_init_hook',
}
